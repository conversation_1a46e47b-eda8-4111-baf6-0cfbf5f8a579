# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2020.
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: rene <<EMAIL>>\n"
"Language: de_CH\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"Der Authorizationheader muss zwei leerzeichen-getrennte Werte enthalten"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "Der Token ist für keinen Tokentyp gültig"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "Token enthält keine erkennbare Benutzeridentifikation"

#: authentication.py:132
msgid "User not found"
msgstr "Benutzer nicht gefunden"

#: authentication.py:135
msgid "User is inactive"
msgstr "Inaktiver Benutzer"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "Unerkannter Algorithmustyp '{}'"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "Ungültiger oder abgelaufener Token"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr ""

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr "Kein aktiver Account mit diesen Zugangsdaten gefunden"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"Die Einstellung '{}' wurde gelöscht. Bitte beachte '{}' für verfügbare "
"Einstellungen."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "Benutzer"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "erstellt am"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "läuft ab am"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Token Blacklist"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "Ein Token ohne Typ oder Lebensdauer kann nicht erstellt werden"

#: tokens.py:116
msgid "Token has no id"
msgstr "Token hat keine Id"

#: tokens.py:128
msgid "Token has no type"
msgstr "Token hat keinen Typ"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "Token hat den falschen Typ"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "Token hat kein '{}' Recht"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "Das Tokenrecht '{}' ist abgelaufen"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "Token steht auf der Blacklist"
