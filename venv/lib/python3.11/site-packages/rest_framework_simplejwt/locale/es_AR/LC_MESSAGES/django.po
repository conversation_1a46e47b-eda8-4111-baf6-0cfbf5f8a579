# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2020.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: djangorestframework_simplejwt\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-02-22 17:30+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language: es_AR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: authentication.py:89
msgid "Authorization header must contain two space-delimited values"
msgstr ""
"El header de autorización debe contener dos valores delimitados por espacio"

#: authentication.py:115
msgid "Given token not valid for any token type"
msgstr "El token dado no es válido para ningún tipo de token"

#: authentication.py:127 authentication.py:162
msgid "Token contained no recognizable user identification"
msgstr "El token no contiene ninguna identificación de usuario"

#: authentication.py:132
msgid "User not found"
msgstr "Usuario no encontrado"

#: authentication.py:135
msgid "User is inactive"
msgstr "El usuario está inactivo"

#: authentication.py:142
msgid "The user's password has been changed."
msgstr ""

#: backends.py:69
msgid "Unrecognized algorithm type '{}'"
msgstr "Tipo de algoritmo no reconocido '{}'"

#: backends.py:75
msgid "You must have cryptography installed to use {}."
msgstr ""

#: backends.py:90
msgid ""
"Unrecognized type '{}', 'leeway' must be of type int, float or timedelta."
msgstr ""

#: backends.py:104 backends.py:154 exceptions.py:47 tokens.py:58
msgid "Token is invalid or expired"
msgstr "El token es inválido o ha expirado"

#: backends.py:152
msgid "Invalid algorithm specified"
msgstr ""

#: serializers.py:35
msgid "No active account found with the given credentials"
msgstr ""
"No se encontró una cuenta de usuario activa para las credenciales dadas"

#: settings.py:73
msgid ""
"The '{}' setting has been removed. Please refer to '{}' for available "
"settings."
msgstr ""
"La configuración '{}' fue removida. Por favor, refiérase a '{}' para "
"consultar las configuraciones disponibles."

#: token_blacklist/admin.py:79
msgid "jti"
msgstr "jti"

#: token_blacklist/admin.py:85
msgid "user"
msgstr "usuario"

#: token_blacklist/admin.py:91
msgid "created at"
msgstr "creado en"

#: token_blacklist/admin.py:97
msgid "expires at"
msgstr "expira en"

#: token_blacklist/apps.py:7
msgid "Token Blacklist"
msgstr "Lista negra de Tokens"

#: tokens.py:44
msgid "Cannot create token with no type or lifetime"
msgstr "No es posible crear un token sin tipo o tiempo de vida"

#: tokens.py:116
msgid "Token has no id"
msgstr "El token no tiene id"

#: tokens.py:128
msgid "Token has no type"
msgstr "El token no tiene tipo"

#: tokens.py:131
msgid "Token has wrong type"
msgstr "El token tiene un tipo incorrecto"

#: tokens.py:190
msgid "Token has no '{}' claim"
msgstr "El token no tiene el privilegio '{}'"

#: tokens.py:195
msgid "Token '{}' claim has expired"
msgstr "El privilegio '{}' del token ha expirado"

#: tokens.py:257
msgid "Token is blacklisted"
msgstr "El token está en la lista negra"
