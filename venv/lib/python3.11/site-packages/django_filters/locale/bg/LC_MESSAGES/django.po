# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON><PERSON> <<EMAIL>>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: django-filter\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-02-10 11:07+0000\n"
"PO-Revision-Date: 2019-12-21 19:36+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: bg\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.9\n"

#: conf.py:16
msgid "date"
msgstr "дата"

#: conf.py:17
msgid "year"
msgstr "година"

#: conf.py:18
msgid "month"
msgstr "месец"

#: conf.py:19
msgid "day"
msgstr "ден"

#: conf.py:20
msgid "week day"
msgstr "ден от седмицата"

#: conf.py:21
msgid "hour"
msgstr "час"

#: conf.py:22
msgid "minute"
msgstr "минута"

#: conf.py:23
msgid "second"
msgstr "секунда"

#: conf.py:27 conf.py:28
msgid "contains"
msgstr "съдържа"

#: conf.py:29
msgid "is in"
msgstr "в"

#: conf.py:30
msgid "is greater than"
msgstr "е по-голям от"

#: conf.py:31
msgid "is greater than or equal to"
msgstr "е по-голям или равен на"

#: conf.py:32
msgid "is less than"
msgstr "е по-малък от"

#: conf.py:33
msgid "is less than or equal to"
msgstr "е по-малък или равен на"

#: conf.py:34 conf.py:35
msgid "starts with"
msgstr "започва с"

#: conf.py:36 conf.py:37
msgid "ends with"
msgstr "завършва с"

#: conf.py:38
msgid "is in range"
msgstr "е в диапазона"

#: conf.py:39
msgid "is null"
msgstr ""

#: conf.py:40 conf.py:41
msgid "matches regex"
msgstr "съвпада с регуларен израз"

#: conf.py:42 conf.py:49
msgid "search"
msgstr "търсене"

#: conf.py:44
msgid "is contained by"
msgstr "се съдържа от"

#: conf.py:45
msgid "overlaps"
msgstr "припокрива"

#: conf.py:46
msgid "has key"
msgstr "има ключ"

#: conf.py:47
msgid "has keys"
msgstr "има ключове"

#: conf.py:48
msgid "has any keys"
msgstr "има който и да е ключ"

#: fields.py:94
msgid "Select a lookup."
msgstr "Изберете справка"

#: fields.py:198
msgid "Range query expects two values."
msgstr "Търсенето по диапазон изисква две стойности"

#: filters.py:437
msgid "Today"
msgstr "Днес"

#: filters.py:438
msgid "Yesterday"
msgstr "Вчера"

#: filters.py:439
msgid "Past 7 days"
msgstr "Последните 7 дни"

#: filters.py:440
msgid "This month"
msgstr "Този месец"

#: filters.py:441
msgid "This year"
msgstr "Тази година"

#: filters.py:543
msgid "Multiple values may be separated by commas."
msgstr "Множество стойности може да се разделят със запетая"

#: filters.py:721
#, python-format
msgid "%s (descending)"
msgstr "%s (намалавящ)"

#: filters.py:737
msgid "Ordering"
msgstr "Подредба"

#: rest_framework/filterset.py:33
#: templates/django_filters/rest_framework/form.html:5
msgid "Submit"
msgstr "Изпращане"

#: templates/django_filters/rest_framework/crispy_form.html:4
#: templates/django_filters/rest_framework/form.html:2
msgid "Field filters"
msgstr "Филтри на полетата"

#: utils.py:308
msgid "exclude"
msgstr "изключва"

#: widgets.py:58
msgid "All"
msgstr "Всичко"

#: widgets.py:162
msgid "Unknown"
msgstr "Неизвестен"

#: widgets.py:162
msgid "Yes"
msgstr "Да"

#: widgets.py:162
msgid "No"
msgstr "Не"
