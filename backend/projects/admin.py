from django.contrib import admin
from django.utils.html import format_html
from .models import Project, ProjectStatus, ProjectPriority


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'status_badge',
        'priority_badge',
        'progress_bar',
        'start_date',
        'expected_end_date',
        'project_manager',
        'budget',
        'is_overdue_display'
    ]

    list_filter = [
        'status',
        'priority',
        'start_date',
        'expected_end_date',
        'project_manager',
        'created_at'
    ]

    search_fields = [
        'name',
        'description',
        'domain_link',
        'project_manager__username',
        'project_manager__first_name',
        'project_manager__last_name'
    ]

    readonly_fields = [
        'created_at',
        'updated_at',
        'is_overdue_display',
        'days_remaining_display',
        'budget_utilization_display',
        'hours_utilization_display'
    ]

    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('name', 'description', 'project_manager')
        }),
        ('التواريخ والجدولة', {
            'fields': ('start_date', 'expected_end_date', 'actual_end_date')
        }),
        ('إدارة المشروع', {
            'fields': ('status', 'priority', 'progress_percentage')
        }),
        ('الموارد والساعات', {
            'fields': ('estimated_hours', 'actual_hours', 'hours_utilization_display')
        }),
        ('التفاصيل التقنية', {
            'fields': ('implementation_phases', 'technologies_used'),
            'classes': ('collapse',)
        }),
        ('معلومات الدومين', {
            'fields': ('domain_link', 'domain_email', 'domain_password'),
            'classes': ('collapse',)
        }),
        ('معلومات السيرفر', {
            'fields': ('server_link', 'server_email', 'server_password'),
            'classes': ('collapse',)
        }),
        ('التواصل', {
            'fields': ('whatsapp_group_link', 'slack_channel'),
            'classes': ('collapse',)
        }),
        ('المالية', {
            'fields': ('budget', 'actual_cost', 'budget_utilization_display')
        }),
        ('معلومات النظام', {
            'fields': ('created_by', 'created_at', 'updated_at', 'is_overdue_display', 'days_remaining_display'),
            'classes': ('collapse',)
        }),
    )

    def status_badge(self, obj):
        """Display status with colored badge"""
        colors = {
            ProjectStatus.PLANNING: '#6c757d',
            ProjectStatus.IN_PROGRESS: '#007bff',
            ProjectStatus.TESTING: '#ffc107',
            ProjectStatus.COMPLETED: '#28a745',
            ProjectStatus.ON_HOLD: '#fd7e14',
            ProjectStatus.CANCELLED: '#dc3545',
        }
        color = colors.get(obj.status, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_status_display()
        )
    status_badge.short_description = 'الحالة'

    def priority_badge(self, obj):
        """Display priority with colored badge"""
        colors = {
            ProjectPriority.LOW: '#28a745',
            ProjectPriority.MEDIUM: '#ffc107',
            ProjectPriority.HIGH: '#fd7e14',
            ProjectPriority.URGENT: '#dc3545',
        }
        color = colors.get(obj.priority, '#6c757d')
        return format_html(
            '<span style="background-color: {}; color: white; padding: 3px 8px; border-radius: 3px; font-size: 11px;">{}</span>',
            color, obj.get_priority_display()
        )
    priority_badge.short_description = 'الأولوية'

    def progress_bar(self, obj):
        """Display progress as a visual bar"""
        return format_html(
            '<div style="width: 100px; background-color: #e9ecef; border-radius: 3px;">'
            '<div style="width: {}%; background-color: #007bff; height: 20px; border-radius: 3px; text-align: center; color: white; font-size: 11px; line-height: 20px;">'
            '{}%</div></div>',
            obj.progress_percentage, obj.progress_percentage
        )
    progress_bar.short_description = 'نسبة الإنجاز'

    def is_overdue_display(self, obj):
        """Display overdue status"""
        if obj.is_overdue:
            return format_html('<span style="color: red; font-weight: bold;">متأخر</span>')
        return format_html('<span style="color: green;">في الموعد</span>')
    is_overdue_display.short_description = 'حالة التأخير'

    def days_remaining_display(self, obj):
        """Display days remaining"""
        days = obj.days_remaining
        if days < 0:
            return format_html('<span style="color: red;">{} يوم متأخر</span>', abs(days))
        elif days == 0:
            return format_html('<span style="color: orange;">ينتهي اليوم</span>')
        else:
            return format_html('<span style="color: green;">{} يوم متبقي</span>', days)
    days_remaining_display.short_description = 'الأيام المتبقية'

    def budget_utilization_display(self, obj):
        """Display budget utilization percentage"""
        utilization = obj.budget_utilization
        if utilization > 100:
            color = 'red'
        elif utilization > 80:
            color = 'orange'
        else:
            color = 'green'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, utilization)
    budget_utilization_display.short_description = 'استخدام الميزانية'

    def hours_utilization_display(self, obj):
        """Display hours utilization percentage"""
        utilization = obj.hours_utilization
        if utilization > 100:
            color = 'red'
        elif utilization > 80:
            color = 'orange'
        else:
            color = 'green'
        return format_html('<span style="color: {};">{:.1f}%</span>', color, utilization)
    hours_utilization_display.short_description = 'استخدام الساعات'

    def save_model(self, request, obj, form, change):
        """Auto-set created_by field"""
        if not change:  # Only set on creation
            obj.created_by = request.user
        super().save_model(request, obj, form, change)
