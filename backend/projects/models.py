from django.db import models
from django.core.validators import URLValidator, MinValueValidator, MaxValueValidator
from django.contrib.auth.models import User
from django.utils import timezone


class ProjectStatus(models.TextChoices):
    PLANNING = 'planning', 'التخطيط'
    IN_PROGRESS = 'in_progress', 'قيد التنفيذ'
    TESTING = 'testing', 'الاختبار'
    COMPLETED = 'completed', 'مكتمل'
    ON_HOLD = 'on_hold', 'متوقف مؤقتاً'
    CANCELLED = 'cancelled', 'ملغي'


class ProjectPriority(models.TextChoices):
    LOW = 'low', 'منخفضة'
    MEDIUM = 'medium', 'متوسطة'
    HIGH = 'high', 'عالية'
    URGENT = 'urgent', 'عاجل'


class Project(models.Model):
    # Basic Information
    name = models.CharField(
        max_length=200,
        verbose_name="اسم المشروع",
        db_index=True,
        help_text="اسم المشروع الذي سيظهر في النظام"
    )
    description = models.TextField(
        blank=True,
        verbose_name="وصف المشروع",
        help_text="وصف تفصيلي للمشروع ومتطلباته"
    )

    # Client relationship - will be created later
    # client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, verbose_name="العميل")

    # Dates and Timeline
    start_date = models.DateField(
        verbose_name="تاريخ البداية",
        db_index=True,
        help_text="تاريخ بداية المشروع"
    )
    expected_end_date = models.DateField(
        verbose_name="تاريخ الانتهاء المتوقع",
        help_text="التاريخ المتوقع لانتهاء المشروع"
    )
    actual_end_date = models.DateField(
        null=True,
        blank=True,
        verbose_name="تاريخ الانتهاء الفعلي",
        help_text="التاريخ الفعلي لانتهاء المشروع"
    )

    # Project Management
    status = models.CharField(
        max_length=20,
        choices=ProjectStatus.choices,
        default=ProjectStatus.PLANNING,
        verbose_name="حالة المشروع",
        db_index=True
    )
    priority = models.CharField(
        max_length=10,
        choices=ProjectPriority.choices,
        default=ProjectPriority.MEDIUM,
        verbose_name="الأولوية",
        db_index=True
    )
    progress_percentage = models.PositiveIntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name="نسبة الإنجاز",
        help_text="نسبة إنجاز المشروع من 0 إلى 100"
    )

    # Team and Resources
    project_manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='managed_projects',
        verbose_name="مدير المشروع",
        help_text="المسؤول عن إدارة المشروع"
    )
    # project_team = models.ManyToManyField('developers.Developer', blank=True, verbose_name="فريق المشروع")
    estimated_hours = models.PositiveIntegerField(
        default=0,
        verbose_name="الساعات المقدرة",
        help_text="عدد الساعات المقدرة لإنجاز المشروع"
    )
    actual_hours = models.PositiveIntegerField(
        default=0,
        verbose_name="الساعات الفعلية",
        help_text="عدد الساعات الفعلية المستغرقة"
    )

    # Technical Details
    implementation_phases = models.JSONField(
        default=list,
        verbose_name="مراحل التنفيذ",
        help_text="قائمة بمراحل تنفيذ المشروع"
    )
    technologies_used = models.JSONField(
        default=list,
        verbose_name="التقنيات المستخدمة",
        help_text="قائمة بالتقنيات والأدوات المستخدمة"
    )

    # Domain and Server Information
    domain_link = models.URLField(
        blank=True,
        null=True,
        verbose_name="رابط الدومين",
        help_text="رابط الموقع الإلكتروني للمشروع"
    )
    domain_email = models.EmailField(
        blank=True,
        verbose_name="إيميل الدومين",
        help_text="البريد الإلكتروني المرتبط بالدومين"
    )
    domain_password = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="كلمة مرور الدومين",
        help_text="كلمة مرور لوحة تحكم الدومين"
    )
    server_link = models.URLField(
        blank=True,
        null=True,
        verbose_name="رابط السيرفر",
        help_text="رابط لوحة تحكم السيرفر"
    )
    server_email = models.EmailField(
        blank=True,
        verbose_name="إيميل السيرفر",
        help_text="البريد الإلكتروني لحساب السيرفر"
    )
    server_password = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="كلمة مرور السيرفر",
        help_text="كلمة مرور لوحة تحكم السيرفر"
    )

    # Communication
    whatsapp_group_link = models.URLField(
        blank=True,
        null=True,
        verbose_name="رابط مجموعة الواتساب",
        help_text="رابط مجموعة الواتساب الخاصة بالمشروع"
    )
    slack_channel = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="قناة سلاك",
        help_text="اسم قناة سلاك للتواصل"
    )

    # Financial
    budget = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name="الميزانية",
        help_text="الميزانية المخصصة للمشروع بالجنيه المصري"
    )
    actual_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name="التكلفة الفعلية",
        help_text="التكلفة الفعلية للمشروع"
    )

    # Metadata
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="تاريخ الإنشاء"
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name="تاريخ التحديث"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_projects',
        verbose_name="أنشئ بواسطة"
    )

    class Meta:
        verbose_name = "مشروع"
        verbose_name_plural = "المشاريع"
        ordering = ['-start_date', '-priority']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['start_date', 'expected_end_date']),
            # models.Index(fields=['client', 'status']),  # Will be uncommented when client model is created
        ]

    def __str__(self):
        return f"{self.name}"
        # return f"{self.name} - {self.client.name}"  # Will be updated when client model is created

    @property
    def is_overdue(self):
        """Check if project is overdue"""
        return self.expected_end_date < timezone.now().date() and self.status != ProjectStatus.COMPLETED

    @property
    def days_remaining(self):
        """Calculate days remaining until expected end date"""
        if self.status == ProjectStatus.COMPLETED:
            return 0
        return (self.expected_end_date - timezone.now().date()).days

    @property
    def budget_utilization(self):
        """Calculate budget utilization percentage"""
        if self.budget == 0:
            return 0
        return (self.actual_cost / self.budget) * 100

    @property
    def hours_utilization(self):
        """Calculate hours utilization percentage"""
        if self.estimated_hours == 0:
            return 0
        return (self.actual_hours / self.estimated_hours) * 100
