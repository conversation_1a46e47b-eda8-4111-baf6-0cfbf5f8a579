# Generated by Django 5.0 on 2025-05-31 01:09

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Project",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        db_index=True,
                        help_text="اسم المشروع الذي سيظهر في النظام",
                        max_length=200,
                        verbose_name="اسم المشروع",
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="وصف تفصيلي للمشروع ومتطلباته",
                        verbose_name="وصف المشروع",
                    ),
                ),
                (
                    "start_date",
                    models.DateField(
                        db_index=True,
                        help_text="تاريخ بداية المشروع",
                        verbose_name="تاريخ البداية",
                    ),
                ),
                (
                    "expected_end_date",
                    models.DateField(
                        help_text="التاريخ المتوقع لانتهاء المشروع",
                        verbose_name="تاريخ الانتهاء المتوقع",
                    ),
                ),
                (
                    "actual_end_date",
                    models.DateField(
                        blank=True,
                        help_text="التاريخ الفعلي لانتهاء المشروع",
                        null=True,
                        verbose_name="تاريخ الانتهاء الفعلي",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("planning", "التخطيط"),
                            ("in_progress", "قيد التنفيذ"),
                            ("testing", "الاختبار"),
                            ("completed", "مكتمل"),
                            ("on_hold", "متوقف مؤقتاً"),
                            ("cancelled", "ملغي"),
                        ],
                        db_index=True,
                        default="planning",
                        max_length=20,
                        verbose_name="حالة المشروع",
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "منخفضة"),
                            ("medium", "متوسطة"),
                            ("high", "عالية"),
                            ("urgent", "عاجل"),
                        ],
                        db_index=True,
                        default="medium",
                        max_length=10,
                        verbose_name="الأولوية",
                    ),
                ),
                (
                    "progress_percentage",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="نسبة إنجاز المشروع من 0 إلى 100",
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                        verbose_name="نسبة الإنجاز",
                    ),
                ),
                (
                    "estimated_hours",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="عدد الساعات المقدرة لإنجاز المشروع",
                        verbose_name="الساعات المقدرة",
                    ),
                ),
                (
                    "actual_hours",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="عدد الساعات الفعلية المستغرقة",
                        verbose_name="الساعات الفعلية",
                    ),
                ),
                (
                    "implementation_phases",
                    models.JSONField(
                        default=list,
                        help_text="قائمة بمراحل تنفيذ المشروع",
                        verbose_name="مراحل التنفيذ",
                    ),
                ),
                (
                    "technologies_used",
                    models.JSONField(
                        default=list,
                        help_text="قائمة بالتقنيات والأدوات المستخدمة",
                        verbose_name="التقنيات المستخدمة",
                    ),
                ),
                (
                    "domain_link",
                    models.URLField(
                        blank=True,
                        help_text="رابط الموقع الإلكتروني للمشروع",
                        null=True,
                        verbose_name="رابط الدومين",
                    ),
                ),
                (
                    "domain_email",
                    models.EmailField(
                        blank=True,
                        help_text="البريد الإلكتروني المرتبط بالدومين",
                        max_length=254,
                        verbose_name="إيميل الدومين",
                    ),
                ),
                (
                    "domain_password",
                    models.CharField(
                        blank=True,
                        help_text="كلمة مرور لوحة تحكم الدومين",
                        max_length=100,
                        verbose_name="كلمة مرور الدومين",
                    ),
                ),
                (
                    "server_link",
                    models.URLField(
                        blank=True,
                        help_text="رابط لوحة تحكم السيرفر",
                        null=True,
                        verbose_name="رابط السيرفر",
                    ),
                ),
                (
                    "server_email",
                    models.EmailField(
                        blank=True,
                        help_text="البريد الإلكتروني لحساب السيرفر",
                        max_length=254,
                        verbose_name="إيميل السيرفر",
                    ),
                ),
                (
                    "server_password",
                    models.CharField(
                        blank=True,
                        help_text="كلمة مرور لوحة تحكم السيرفر",
                        max_length=100,
                        verbose_name="كلمة مرور السيرفر",
                    ),
                ),
                (
                    "whatsapp_group_link",
                    models.URLField(
                        blank=True,
                        help_text="رابط مجموعة الواتساب الخاصة بالمشروع",
                        null=True,
                        verbose_name="رابط مجموعة الواتساب",
                    ),
                ),
                (
                    "slack_channel",
                    models.CharField(
                        blank=True,
                        help_text="اسم قناة سلاك للتواصل",
                        max_length=100,
                        verbose_name="قناة سلاك",
                    ),
                ),
                (
                    "budget",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="الميزانية المخصصة للمشروع بالجنيه المصري",
                        max_digits=10,
                        verbose_name="الميزانية",
                    ),
                ),
                (
                    "actual_cost",
                    models.DecimalField(
                        decimal_places=2,
                        default=0,
                        help_text="التكلفة الفعلية للمشروع",
                        max_digits=10,
                        verbose_name="التكلفة الفعلية",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="تاريخ الإنشاء"
                    ),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث"),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="created_projects",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="أنشئ بواسطة",
                    ),
                ),
                (
                    "project_manager",
                    models.ForeignKey(
                        help_text="المسؤول عن إدارة المشروع",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="managed_projects",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="مدير المشروع",
                    ),
                ),
            ],
            options={
                "verbose_name": "مشروع",
                "verbose_name_plural": "المشاريع",
                "ordering": ["-start_date", "-priority"],
                "indexes": [
                    models.Index(
                        fields=["status", "priority"],
                        name="projects_pr_status_d9e5a3_idx",
                    ),
                    models.Index(
                        fields=["start_date", "expected_end_date"],
                        name="projects_pr_start_d_3bff1b_idx",
                    ),
                ],
            },
        ),
    ]
