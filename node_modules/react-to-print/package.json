{"name": "react-to-print", "version": "3.1.0", "description": "Print React components in the browser", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "NODE_ENV=production webpack --progress", "lint": "eslint ./src", "prepare": "npm run lint && npm run build && husky install", "start": "NODE_ENV=development webpack serve"}, "repository": {"type": "git", "url": "git+https://github.com/MatthewHerbst/react-to-print.git"}, "keywords": ["react", "print", "reactjs", "react-to-print"], "author": "<PERSON> <MatthewHerbst.com>", "contributors": ["gregnb <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/MatthewHerbst/react-to-print/issues"}, "homepage": "https://github.com/MatthewHerbst/react-to-print#readme", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ~19"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.2", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "lint-staged": "^15.2.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-tabs": "^6.1.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.1", "tslib": "^2.8.1", "typescript": "^5.7.2", "typescript-eslint": "^8.16.1", "url-loader": "^4.1.1", "webpack": "^5.97.1", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0"}, "lint-staged": {"*.{ts,tsx}": ["eslint"]}}