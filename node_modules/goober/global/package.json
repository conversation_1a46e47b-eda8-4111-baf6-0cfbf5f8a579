{"name": "goober-global", "amdName": "gooberGlobal", "version": "0.0.1", "description": "The createGlobalStyles addon function for goober", "sideEffects": false, "main": "dist/goober-global.cjs", "module": "dist/goober-global.esm.js", "umd:main": "dist/goober-global.umd.js", "source": "src/index.js", "unpkg": "dist/goober-global.umd.js", "types": "./global.d.ts", "type": "module", "scripts": {"build": "rm -rf dist && microbundle --entry src/index.js --name gooberGlobal --no-sourcemap --generateTypes false", "test": "jest"}, "repository": {"type": "git", "url": "https://github.com/cristianbote/goober.git", "directory": "global"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "keywords": ["goober", "styled", "global"], "license": "MIT", "peerDependencies": {"goober": "^2.0.29"}, "devDependencies": {"goober": "^2.0.29", "microbundle": "^0.14.2", "jest": "^24.1.0", "preact": "^10.5.6", "@babel/plugin-transform-react-jsx": "^7.7.0", "@babel/preset-env": "^7.3.1", "babel-jest": "^24.1.0"}}