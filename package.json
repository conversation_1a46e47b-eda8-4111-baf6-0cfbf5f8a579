{"name": "nextjs_erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@fontsource/ibm-plex-sans-arabic": "^5.2.5", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.79.0", "@tanstack/react-query-devtools": "^5.79.0", "@tanstack/react-table": "^8.21.3", "clsx": "^2.1.1", "date-fns": "^2.30.0", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "15.3.3", "next-auth": "^5.0.0-beta.28", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-rtl": "^0.9.0", "xlsx": "^0.18.5", "zod": "^3.25.42", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}