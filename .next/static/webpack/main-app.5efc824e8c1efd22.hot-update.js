"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("main-app",{

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/client/app-find-source-map-url.js ***!
  \******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"findSourceMapURL\", ({\n    enumerable: true,\n    get: function() {\n        return findSourceMapURL;\n    }\n}));\nconst basePath =  false || '';\nconst pathname = \"\" + basePath + \"/__nextjs_source-map\";\nconst findSourceMapURL =  true ? function findSourceMapURL(filename) {\n    if (filename === '') {\n        return null;\n    }\n    if (filename.startsWith(document.location.origin) && filename.includes('/_next/static')) {\n        // This is a request for a client chunk. This can only happen when\n        // using Turbopack. In this case, since we control how those source\n        // maps are generated, we can safely assume that the sourceMappingURL\n        // is relative to the filename, with an added `.map` extension. The\n        // browser can just request this file, and it gets served through the\n        // normal dev server, without the need to route this through\n        // the `/__nextjs_source-map` dev middleware.\n        return \"\" + filename + \".map\";\n    }\n    const url = new URL(pathname, document.location.origin);\n    url.searchParams.set('filename', filename);\n    return url.href;\n} : 0;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-find-source-map-url.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-index.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/client/app-index.js ***!
  \****************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// imports polyfill from `@next/polyfill-module` after build.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hydrate\", ({\n    enumerable: true,\n    get: function() {\n        return hydrate;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n__webpack_require__(/*! ../build/polyfills/polyfill-module */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/polyfill-module.js\");\n__webpack_require__(/*! ./components/globals/patch-console */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/patch-console.js\");\n__webpack_require__(/*! ./components/globals/handle-global-errors */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/handle-global-errors.js\");\nconst _client = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react-dom/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/client.js\"));\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _client1 = __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nconst _headmanagercontextsharedruntime = __webpack_require__(/*! ../shared/lib/head-manager-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/head-manager-context.shared-runtime.js\");\nconst _onrecoverableerror = __webpack_require__(/*! ./react-client-callbacks/on-recoverable-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\");\nconst _errorboundarycallbacks = __webpack_require__(/*! ./react-client-callbacks/error-boundary-callbacks */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\");\nconst _appcallserver = __webpack_require__(/*! ./app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ./app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _approuterinstance = __webpack_require__(/*! ./components/app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./components/app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\"));\nconst _createinitialrouterstate = __webpack_require__(/*! ./components/router-reducer/create-initial-router-state */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _appbuildid = __webpack_require__(/*! ./app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\n/// <reference types=\"react-dom/experimental\" />\nconst appElement = document;\nconst encoder = new TextEncoder();\nlet initialServerDataBuffer = undefined;\nlet initialServerDataWriter = undefined;\nlet initialServerDataLoaded = false;\nlet initialServerDataFlushed = false;\nlet initialFormStateData = null;\nfunction nextServerDataCallback(seg) {\n    if (seg[0] === 0) {\n        initialServerDataBuffer = [];\n    } else if (seg[0] === 1) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(encoder.encode(seg[1]));\n        } else {\n            initialServerDataBuffer.push(seg[1]);\n        }\n    } else if (seg[0] === 2) {\n        initialFormStateData = seg[1];\n    } else if (seg[0] === 3) {\n        if (!initialServerDataBuffer) throw Object.defineProperty(new Error('Unexpected server data: missing bootstrap script.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E18\",\n            enumerable: false,\n            configurable: true\n        });\n        // Decode the base64 string back to binary data.\n        const binaryString = atob(seg[1]);\n        const decodedChunk = new Uint8Array(binaryString.length);\n        for(var i = 0; i < binaryString.length; i++){\n            decodedChunk[i] = binaryString.charCodeAt(i);\n        }\n        if (initialServerDataWriter) {\n            initialServerDataWriter.enqueue(decodedChunk);\n        } else {\n            initialServerDataBuffer.push(decodedChunk);\n        }\n    }\n}\nfunction isStreamErrorOrUnfinished(ctr) {\n    // If `desiredSize` is null, it means the stream is closed or errored. If it is lower than 0, the stream is still unfinished.\n    return ctr.desiredSize === null || ctr.desiredSize < 0;\n}\n// There might be race conditions between `nextServerDataRegisterWriter` and\n// `DOMContentLoaded`. The former will be called when React starts to hydrate\n// the root, the latter will be called when the DOM is fully loaded.\n// For streaming, the former is called first due to partial hydration.\n// For non-streaming, the latter can be called first.\n// Hence, we use two variables `initialServerDataLoaded` and\n// `initialServerDataFlushed` to make sure the writer will be closed and\n// `initialServerDataBuffer` will be cleared in the right time.\nfunction nextServerDataRegisterWriter(ctr) {\n    if (initialServerDataBuffer) {\n        initialServerDataBuffer.forEach((val)=>{\n            ctr.enqueue(typeof val === 'string' ? encoder.encode(val) : val);\n        });\n        if (initialServerDataLoaded && !initialServerDataFlushed) {\n            if (isStreamErrorOrUnfinished(ctr)) {\n                ctr.error(Object.defineProperty(new Error('The connection to the page was unexpectedly closed, possibly due to the stop button being clicked, loss of Wi-Fi, or an unstable internet connection.'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E117\",\n                    enumerable: false,\n                    configurable: true\n                }));\n            } else {\n                ctr.close();\n            }\n            initialServerDataFlushed = true;\n            initialServerDataBuffer = undefined;\n        }\n    }\n    initialServerDataWriter = ctr;\n}\n// When `DOMContentLoaded`, we can close all pending writers to finish hydration.\nconst DOMContentLoaded = function() {\n    if (initialServerDataWriter && !initialServerDataFlushed) {\n        initialServerDataWriter.close();\n        initialServerDataFlushed = true;\n        initialServerDataBuffer = undefined;\n    }\n    initialServerDataLoaded = true;\n};\n_c = DOMContentLoaded;\n// It's possible that the DOM is already loaded.\nif (document.readyState === 'loading') {\n    document.addEventListener('DOMContentLoaded', DOMContentLoaded, false);\n} else {\n    // Delayed in marco task to ensure it's executed later than hydration\n    setTimeout(DOMContentLoaded);\n}\nconst nextServerDataLoadingGlobal = self.__next_f = self.__next_f || [];\nnextServerDataLoadingGlobal.forEach(nextServerDataCallback);\nnextServerDataLoadingGlobal.push = nextServerDataCallback;\nconst readable = new ReadableStream({\n    start (controller) {\n        nextServerDataRegisterWriter(controller);\n    }\n});\nconst initialServerResponse = (0, _client1.createFromReadableStream)(readable, {\n    callServer: _appcallserver.callServer,\n    findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n});\nfunction ServerRoot(param) {\n    let { pendingActionQueue } = param;\n    const initialRSCPayload = (0, _react.use)(initialServerResponse);\n    const actionQueue = (0, _react.use)(pendingActionQueue);\n    const router = /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuter.default, {\n        actionQueue: actionQueue,\n        globalErrorComponentAndStyles: initialRSCPayload.G,\n        assetPrefix: initialRSCPayload.p\n    });\n    if ( true && initialRSCPayload.m) {\n        // We provide missing slot information in a context provider only during development\n        // as we log some additional information about the missing slots in the console.\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.MissingSlotContext, {\n            value: initialRSCPayload.m,\n            children: router\n        });\n    }\n    return router;\n}\n_c1 = ServerRoot;\nconst StrictModeIfEnabled =  true ? _react.default.StrictMode : 0;\nfunction Root(param) {\n    let { children } = param;\n    if (false) {}\n    return children;\n}\n_c2 = Root;\nconst reactRootOptions = {\n    onRecoverableError: _onrecoverableerror.onRecoverableError,\n    onCaughtError: _errorboundarycallbacks.onCaughtError,\n    onUncaughtError: _errorboundarycallbacks.onUncaughtError\n};\nfunction hydrate(instrumentationHooks) {\n    // React overrides `.then` and doesn't return a new promise chain,\n    // so we wrap the action queue in a promise to ensure that its value\n    // is defined when the promise resolves.\n    // https://github.com/facebook/react/blob/163365a07872337e04826c4f501565d43dbd2fd4/packages/react-client/src/ReactFlightClient.js#L189-L190\n    const pendingActionQueue = new Promise((resolve, reject)=>{\n        initialServerResponse.then((initialRSCPayload)=>{\n            // setAppBuildId should be called only once, during JS initialization\n            // and before any components have hydrated.\n            (0, _appbuildid.setAppBuildId)(initialRSCPayload.b);\n            const initialTimestamp = Date.now();\n            resolve((0, _approuterinstance.createMutableActionQueue)((0, _createinitialrouterstate.createInitialRouterState)({\n                navigatedAt: initialTimestamp,\n                initialFlightData: initialRSCPayload.f,\n                initialCanonicalUrlParts: initialRSCPayload.c,\n                initialParallelRoutes: new Map(),\n                location: window.location,\n                couldBeIntercepted: initialRSCPayload.i,\n                postponed: initialRSCPayload.s,\n                prerendered: initialRSCPayload.S\n            }), instrumentationHooks));\n        }, (err)=>reject(err));\n    });\n    const reactEl = /*#__PURE__*/ (0, _jsxruntime.jsx)(StrictModeIfEnabled, {\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_headmanagercontextsharedruntime.HeadManagerContext.Provider, {\n            value: {\n                appDir: true\n            },\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Root, {\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(ServerRoot, {\n                    pendingActionQueue: pendingActionQueue\n                })\n            })\n        })\n    });\n    if (document.documentElement.id === '__next_error__') {\n        let element = reactEl;\n        // Server rendering failed, fall back to client-side rendering\n        if (true) {\n            const { createRootLevelDevOverlayElement } = __webpack_require__(/*! ./components/react-dev-overlay/app/client-entry */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/client-entry.js\");\n            // Note this won't cause hydration mismatch because we are doing CSR w/o hydration\n            element = createRootLevelDevOverlayElement(element);\n        }\n        _client.default.createRoot(appElement, reactRootOptions).render(element);\n    } else {\n        _react.default.startTransition(()=>{\n            _client.default.hydrateRoot(appElement, reactEl, {\n                ...reactRootOptions,\n                formState: initialFormStateData\n            });\n        });\n    }\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const { linkGc } = __webpack_require__(/*! ./app-link-gc */ \"(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\");\n        linkGc();\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-index.js.map\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DOMContentLoaded\");\n$RefreshReg$(_c1, \"ServerRoot\");\n$RefreshReg$(_c2, \"Root\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/client/app-link-gc.js ***!
  \******************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"linkGc\", ({\n    enumerable: true,\n    get: function() {\n        return linkGc;\n    }\n}));\nfunction linkGc() {\n    // TODO-APP: Remove this logic when Float has GC built-in in development.\n    if (true) {\n        const callback = (mutationList)=>{\n            for (const mutation of mutationList){\n                if (mutation.type === 'childList') {\n                    for (const node of mutation.addedNodes){\n                        if ('tagName' in node && node.tagName === 'LINK') {\n                            var _link_dataset_precedence;\n                            const link = node;\n                            if ((_link_dataset_precedence = link.dataset.precedence) == null ? void 0 : _link_dataset_precedence.startsWith('next')) {\n                                const href = link.getAttribute('href');\n                                if (href) {\n                                    const [resource, version] = href.split('?v=', 2);\n                                    if (version) {\n                                        const currentOrigin = window.location.origin;\n                                        const allLinks = [\n                                            ...document.querySelectorAll('link[href^=\"' + resource + '\"]'),\n                                            // It's possible that the resource is a full URL or only pathname,\n                                            // so we need to remove the alternative href as well.\n                                            ...document.querySelectorAll('link[href^=\"' + (resource.startsWith(currentOrigin) ? resource.slice(currentOrigin.length) : currentOrigin + resource) + '\"]')\n                                        ];\n                                        for (const otherLink of allLinks){\n                                            var _otherLink_dataset_precedence;\n                                            if ((_otherLink_dataset_precedence = otherLink.dataset.precedence) == null ? void 0 : _otherLink_dataset_precedence.startsWith('next')) {\n                                                const otherHref = otherLink.getAttribute('href');\n                                                if (otherHref) {\n                                                    const [, otherVersion] = otherHref.split('?v=', 2);\n                                                    if (!otherVersion || +otherVersion < +version) {\n                                                        // Delay the removal of the stylesheet to avoid FOUC\n                                                        // caused by `@font-face` rules, as they seem to be\n                                                        // a couple of ticks delayed between the old and new\n                                                        // styles being swapped even if the font is cached.\n                                                        setTimeout(()=>{\n                                                            otherLink.remove();\n                                                        }, 5);\n                                                        const preloadLink = document.querySelector('link[rel=\"preload\"][as=\"style\"][href=\"' + otherHref + '\"]');\n                                                        if (preloadLink) {\n                                                            preloadLink.remove();\n                                                        }\n                                                    }\n                                                }\n                                            }\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n        };\n        // Create an observer instance linked to the callback function\n        const observer = new MutationObserver(callback);\n        observer.observe(document.head, {\n            childList: true\n        });\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-link-gc.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-link-gc.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js":
/*!*************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router-instance.js ***!
  \*************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createMutableActionQueue: function() {\n        return createMutableActionQueue;\n    },\n    dispatchNavigateAction: function() {\n        return dispatchNavigateAction;\n    },\n    dispatchTraverseAction: function() {\n        return dispatchTraverseAction;\n    },\n    getCurrentAppRouterState: function() {\n        return getCurrentAppRouterState;\n    },\n    publicAppRouterInstance: function() {\n        return publicAppRouterInstance;\n    }\n});\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _routerreducer = __webpack_require__(/*! ./router-reducer/router-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./router-reducer/reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nfunction runRemainingActions(actionQueue, setState) {\n    if (actionQueue.pending !== null) {\n        actionQueue.pending = actionQueue.pending.next;\n        if (actionQueue.pending !== null) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            runAction({\n                actionQueue,\n                action: actionQueue.pending,\n                setState\n            });\n        } else {\n            // No more actions are pending, check if a refresh is needed\n            if (actionQueue.needsRefresh) {\n                actionQueue.needsRefresh = false;\n                actionQueue.dispatch({\n                    type: _routerreducertypes.ACTION_REFRESH,\n                    origin: window.location.origin\n                }, setState);\n            }\n        }\n    }\n}\nasync function runAction(param) {\n    let { actionQueue, action, setState } = param;\n    const prevState = actionQueue.state;\n    actionQueue.pending = action;\n    const payload = action.payload;\n    const actionResult = actionQueue.action(prevState, payload);\n    function handleResult(nextState) {\n        // if we discarded this action, the state should also be discarded\n        if (action.discarded) {\n            return;\n        }\n        actionQueue.state = nextState;\n        runRemainingActions(actionQueue, setState);\n        action.resolve(nextState);\n    }\n    // if the action is a promise, set up a callback to resolve it\n    if ((0, _isthenable.isThenable)(actionResult)) {\n        actionResult.then(handleResult, (err)=>{\n            runRemainingActions(actionQueue, setState);\n            action.reject(err);\n        });\n    } else {\n        handleResult(actionResult);\n    }\n}\nfunction dispatchAction(actionQueue, payload, setState) {\n    let resolvers = {\n        resolve: setState,\n        reject: ()=>{}\n    };\n    // most of the action types are async with the exception of restore\n    // it's important that restore is handled quickly since it's fired on the popstate event\n    // and we don't want to add any delay on a back/forward nav\n    // this only creates a promise for the async actions\n    if (payload.type !== _routerreducertypes.ACTION_RESTORE) {\n        // Create the promise and assign the resolvers to the object.\n        const deferredPromise = new Promise((resolve, reject)=>{\n            resolvers = {\n                resolve,\n                reject\n            };\n        });\n        (0, _react.startTransition)(()=>{\n            // we immediately notify React of the pending promise -- the resolver is attached to the action node\n            // and will be called when the associated action promise resolves\n            setState(deferredPromise);\n        });\n    }\n    const newAction = {\n        payload,\n        next: null,\n        resolve: resolvers.resolve,\n        reject: resolvers.reject\n    };\n    // Check if the queue is empty\n    if (actionQueue.pending === null) {\n        // The queue is empty, so add the action and start it immediately\n        // Mark this action as the last in the queue\n        actionQueue.last = newAction;\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else if (payload.type === _routerreducertypes.ACTION_NAVIGATE || payload.type === _routerreducertypes.ACTION_RESTORE) {\n        // Navigations (including back/forward) take priority over any pending actions.\n        // Mark the pending action as discarded (so the state is never applied) and start the navigation action immediately.\n        actionQueue.pending.discarded = true;\n        // The rest of the current queue should still execute after this navigation.\n        // (Note that it can't contain any earlier navigations, because we always put those into `actionQueue.pending` by calling `runAction`)\n        newAction.next = actionQueue.pending.next;\n        // if the pending action was a server action, mark the queue as needing a refresh once events are processed\n        if (actionQueue.pending.payload.type === _routerreducertypes.ACTION_SERVER_ACTION) {\n            actionQueue.needsRefresh = true;\n        }\n        runAction({\n            actionQueue,\n            action: newAction,\n            setState\n        });\n    } else {\n        // The queue is not empty, so add the action to the end of the queue\n        // It will be started by runRemainingActions after the previous action finishes\n        if (actionQueue.last !== null) {\n            actionQueue.last.next = newAction;\n        }\n        actionQueue.last = newAction;\n    }\n}\nlet globalActionQueue = null;\nfunction createMutableActionQueue(initialState, instrumentationHooks) {\n    const actionQueue = {\n        state: initialState,\n        dispatch: (payload, setState)=>dispatchAction(actionQueue, payload, setState),\n        action: async (state, action)=>{\n            const result = (0, _routerreducer.reducer)(state, action);\n            return result;\n        },\n        pending: null,\n        last: null,\n        onRouterTransitionStart: instrumentationHooks !== null && typeof instrumentationHooks.onRouterTransitionStart === 'function' ? instrumentationHooks.onRouterTransitionStart : null\n    };\n    if (true) {\n        // The action queue is lazily created on hydration, but after that point\n        // it doesn't change. So we can store it in a global rather than pass\n        // it around everywhere via props/context.\n        if (globalActionQueue !== null) {\n            throw Object.defineProperty(new Error('Internal Next.js Error: createMutableActionQueue was called more ' + 'than once'), \"__NEXT_ERROR_CODE\", {\n                value: \"E624\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        globalActionQueue = actionQueue;\n    }\n    return actionQueue;\n}\nfunction getCurrentAppRouterState() {\n    return globalActionQueue !== null ? globalActionQueue.state : null;\n}\nfunction getAppRouterActionQueue() {\n    if (globalActionQueue === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    return globalActionQueue;\n}\nfunction getProfilingHookForOnNavigationStart() {\n    if (globalActionQueue !== null) {\n        return globalActionQueue.onRouterTransitionStart;\n    }\n    return null;\n}\nfunction dispatchNavigateAction(href, navigateType, shouldScroll, linkInstanceRef) {\n    // TODO: This stuff could just go into the reducer. Leaving as-is for now\n    // since we're about to rewrite all the router reducer stuff anyway.\n    const url = new URL((0, _addbasepath.addBasePath)(href), location.href);\n    if (false) {}\n    (0, _links.setLinkForCurrentNavigation)(linkInstanceRef);\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, navigateType);\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_NAVIGATE,\n        url,\n        isExternalUrl: (0, _approuter.isExternalURL)(url),\n        locationSearch: location.search,\n        shouldScroll,\n        navigateType,\n        allowAliasing: true\n    });\n}\nfunction dispatchTraverseAction(href, tree) {\n    const onRouterTransitionStart = getProfilingHookForOnNavigationStart();\n    if (onRouterTransitionStart !== null) {\n        onRouterTransitionStart(href, 'traverse');\n    }\n    (0, _useactionqueue.dispatchAppRouterAction)({\n        type: _routerreducertypes.ACTION_RESTORE,\n        url: new URL(href),\n        tree\n    });\n}\nconst publicAppRouterInstance = {\n    back: ()=>window.history.back(),\n    forward: ()=>window.history.forward(),\n    prefetch:  false ? // cache. So we don't need to dispatch an action.\n    0 : (href, options)=>{\n        // Use the old prefetch implementation.\n        const actionQueue = getAppRouterActionQueue();\n        const url = (0, _approuter.createPrefetchURL)(href);\n        if (url !== null) {\n            var _options_kind;\n            // The prefetch reducer doesn't actually update any state or\n            // trigger a rerender. It just writes to a mutable cache. So we\n            // shouldn't bother calling setState/dispatch; we can just re-run\n            // the reducer directly using the current state.\n            // TODO: Refactor this away from a \"reducer\" so it's\n            // less confusing.\n            (0, _prefetchreducer.prefetchReducer)(actionQueue.state, {\n                type: _routerreducertypes.ACTION_PREFETCH,\n                url,\n                kind: (_options_kind = options == null ? void 0 : options.kind) != null ? _options_kind : _routerreducertypes.PrefetchKind.FULL\n            });\n        }\n    },\n    replace: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'replace', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    push: (href, options)=>{\n        (0, _react.startTransition)(()=>{\n            var _options_scroll;\n            dispatchNavigateAction(href, 'push', (_options_scroll = options == null ? void 0 : options.scroll) != null ? _options_scroll : true, null);\n        });\n    },\n    refresh: ()=>{\n        (0, _react.startTransition)(()=>{\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_REFRESH,\n                origin: window.location.origin\n            });\n        });\n    },\n    hmrRefresh: ()=>{\n        if (false) {} else {\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_HMR_REFRESH,\n                    origin: window.location.origin\n                });\n            });\n        }\n    }\n};\n// Exists for debugging purposes. Don't use in application code.\nif ( true && window.next) {\n    window.next.router = publicAppRouterInstance;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router-instance.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1pbnN0YW5jZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUF5TWdCQSx3QkFBd0I7ZUFBeEJBOztJQTBEQUMsc0JBQXNCO2VBQXRCQTs7SUErQkFDLHNCQUFzQjtlQUF0QkE7O0lBbkRBQyx3QkFBd0I7ZUFBeEJBOztJQXVFSEMsdUJBQXVCO2VBQXZCQTs7O2dEQTFTTjsyQ0FDaUI7bUNBQ1E7d0NBQ0w7MENBQzBCOzRDQUNiO3lDQUNaO3VDQUNxQjs2Q0FDakI7bUNBTStCO0FBNEIvRCxTQUFTQyxvQkFDUEMsV0FBaUMsRUFDakNDLFFBQThCO0lBRTlCLElBQUlELFlBQVlFLE9BQU8sS0FBSyxNQUFNO1FBQ2hDRixZQUFZRSxPQUFPLEdBQUdGLFlBQVlFLE9BQU8sQ0FBQ0MsSUFBSTtRQUM5QyxJQUFJSCxZQUFZRSxPQUFPLEtBQUssTUFBTTtZQUNoQyxtRUFBbUU7WUFDbkVFLFVBQVU7Z0JBQ1JKO2dCQUNBSyxRQUFRTCxZQUFZRSxPQUFPO2dCQUMzQkQ7WUFDRjtRQUNGLE9BQU87WUFDTCw0REFBNEQ7WUFDNUQsSUFBSUQsWUFBWU0sWUFBWSxFQUFFO2dCQUM1Qk4sWUFBWU0sWUFBWSxHQUFHO2dCQUMzQk4sWUFBWU8sUUFBUSxDQUNsQjtvQkFDRUMsTUFBTUMsb0JBQUFBLGNBQWM7b0JBQ3BCQyxRQUFRQyxPQUFPQyxRQUFRLENBQUNGLE1BQU07Z0JBQ2hDLEdBQ0FUO1lBRUo7UUFDRjtJQUNGO0FBQ0Y7QUFFQSxlQUFlRyxVQUFVLEtBUXhCO0lBUndCLE1BQ3ZCSixXQUFXLEVBQ1hLLE1BQU0sRUFDTkosUUFBUSxFQUtULEdBUndCO0lBU3ZCLE1BQU1ZLFlBQVliLFlBQVljLEtBQUs7SUFFbkNkLFlBQVlFLE9BQU8sR0FBR0c7SUFFdEIsTUFBTVUsVUFBVVYsT0FBT1UsT0FBTztJQUM5QixNQUFNQyxlQUFlaEIsWUFBWUssTUFBTSxDQUFDUSxXQUFXRTtJQUVuRCxTQUFTRSxhQUFhQyxTQUF5QjtRQUM3QyxrRUFBa0U7UUFDbEUsSUFBSWIsT0FBT2MsU0FBUyxFQUFFO1lBQ3BCO1FBQ0Y7UUFFQW5CLFlBQVljLEtBQUssR0FBR0k7UUFFcEJuQixvQkFBb0JDLGFBQWFDO1FBQ2pDSSxPQUFPZSxPQUFPLENBQUNGO0lBQ2pCO0lBRUEsOERBQThEO0lBQzlELElBQUlHLENBQUFBLEdBQUFBLFlBQUFBLFVBQVUsRUFBQ0wsZUFBZTtRQUM1QkEsYUFBYU0sSUFBSSxDQUFDTCxjQUFjLENBQUNNO1lBQy9CeEIsb0JBQW9CQyxhQUFhQztZQUNqQ0ksT0FBT21CLE1BQU0sQ0FBQ0Q7UUFDaEI7SUFDRixPQUFPO1FBQ0xOLGFBQWFEO0lBQ2Y7QUFDRjtBQUVBLFNBQVNTLGVBQ1B6QixXQUFpQyxFQUNqQ2UsT0FBdUIsRUFDdkJkLFFBQThCO0lBRTlCLElBQUl5QixZQUdBO1FBQUVOLFNBQVNuQjtRQUFVdUIsUUFBUSxLQUFPO0lBQUU7SUFFMUMsbUVBQW1FO0lBQ25FLHdGQUF3RjtJQUN4RiwyREFBMkQ7SUFDM0Qsb0RBQW9EO0lBQ3BELElBQUlULFFBQVFQLElBQUksS0FBS21CLG9CQUFBQSxjQUFjLEVBQUU7UUFDbkMsNkRBQTZEO1FBQzdELE1BQU1DLGtCQUFrQixJQUFJQyxRQUF3QixDQUFDVCxTQUFTSTtZQUM1REUsWUFBWTtnQkFBRU47Z0JBQVNJO1lBQU87UUFDaEM7UUFFQU0sQ0FBQUEsR0FBQUEsT0FBQUEsZUFBZSxFQUFDO1lBQ2Qsb0dBQW9HO1lBQ3BHLGlFQUFpRTtZQUNqRTdCLFNBQVMyQjtRQUNYO0lBQ0Y7SUFFQSxNQUFNRyxZQUE2QjtRQUNqQ2hCO1FBQ0FaLE1BQU07UUFDTmlCLFNBQVNNLFVBQVVOLE9BQU87UUFDMUJJLFFBQVFFLFVBQVVGLE1BQU07SUFDMUI7SUFFQSw4QkFBOEI7SUFDOUIsSUFBSXhCLFlBQVlFLE9BQU8sS0FBSyxNQUFNO1FBQ2hDLGlFQUFpRTtRQUNqRSw0Q0FBNEM7UUFDNUNGLFlBQVlnQyxJQUFJLEdBQUdEO1FBRW5CM0IsVUFBVTtZQUNSSjtZQUNBSyxRQUFRMEI7WUFDUjlCO1FBQ0Y7SUFDRixPQUFPLElBQ0xjLFFBQVFQLElBQUksS0FBS3lCLG9CQUFBQSxlQUFlLElBQ2hDbEIsUUFBUVAsSUFBSSxLQUFLbUIsb0JBQUFBLGNBQWMsRUFDL0I7UUFDQSwrRUFBK0U7UUFDL0Usb0hBQW9IO1FBQ3BIM0IsWUFBWUUsT0FBTyxDQUFDaUIsU0FBUyxHQUFHO1FBRWhDLDRFQUE0RTtRQUM1RSxzSUFBc0k7UUFDdElZLFVBQVU1QixJQUFJLEdBQUdILFlBQVlFLE9BQU8sQ0FBQ0MsSUFBSTtRQUV6QywyR0FBMkc7UUFDM0csSUFBSUgsWUFBWUUsT0FBTyxDQUFDYSxPQUFPLENBQUNQLElBQUksS0FBSzBCLG9CQUFBQSxvQkFBb0IsRUFBRTtZQUM3RGxDLFlBQVlNLFlBQVksR0FBRztRQUM3QjtRQUVBRixVQUFVO1lBQ1JKO1lBQ0FLLFFBQVEwQjtZQUNSOUI7UUFDRjtJQUNGLE9BQU87UUFDTCxvRUFBb0U7UUFDcEUsK0VBQStFO1FBQy9FLElBQUlELFlBQVlnQyxJQUFJLEtBQUssTUFBTTtZQUM3QmhDLFlBQVlnQyxJQUFJLENBQUM3QixJQUFJLEdBQUc0QjtRQUMxQjtRQUNBL0IsWUFBWWdDLElBQUksR0FBR0Q7SUFDckI7QUFDRjtBQUVBLElBQUlJLG9CQUFpRDtBQUU5QyxTQUFTekMseUJBQ2QwQyxZQUE0QixFQUM1QkMsb0JBQXVEO0lBRXZELE1BQU1yQyxjQUFvQztRQUN4Q2MsT0FBT3NCO1FBQ1A3QixVQUFVLENBQUNRLFNBQXlCZCxXQUNsQ3dCLGVBQWV6QixhQUFhZSxTQUFTZDtRQUN2Q0ksUUFBUSxPQUFPUyxPQUF1QlQ7WUFDcEMsTUFBTWlDLFNBQVNDLENBQUFBLEdBQUFBLGVBQUFBLE9BQUFBLEVBQVF6QixPQUFPVDtZQUM5QixPQUFPaUM7UUFDVDtRQUNBcEMsU0FBUztRQUNUOEIsTUFBTTtRQUNOUSx5QkFDRUgseUJBQXlCLFFBQ3pCLE9BQU9BLHFCQUFxQkcsdUJBQXVCLEtBQUssYUFFcERILHFCQUFxQkcsdUJBQXVCLEdBQzVDO0lBQ1I7SUFFQSxJQUFJLElBQTZCLEVBQUU7UUFDakMsd0VBQXdFO1FBQ3hFLHFFQUFxRTtRQUNyRSwwQ0FBMEM7UUFDMUMsSUFBSUwsc0JBQXNCLE1BQU07WUFDOUIsTUFBTSxxQkFHTCxDQUhLLElBQUlNLE1BQ1Isc0VBQ0UsY0FGRTt1QkFBQTs0QkFBQTs4QkFBQTtZQUdOO1FBQ0Y7UUFDQU4sb0JBQW9CbkM7SUFDdEI7SUFFQSxPQUFPQTtBQUNUO0FBRU8sU0FBU0g7SUFDZCxPQUFPc0Msc0JBQXNCLE9BQU9BLGtCQUFrQnJCLEtBQUssR0FBRztBQUNoRTtBQUVBLFNBQVM0QjtJQUNQLElBQUlQLHNCQUFzQixNQUFNO1FBQzlCLE1BQU0scUJBRUwsQ0FGSyxJQUFJTSxNQUNSLDRFQURJO21CQUFBO3dCQUFBOzBCQUFBO1FBRU47SUFDRjtJQUNBLE9BQU9OO0FBQ1Q7QUFFQSxTQUFTUTtJQUNQLElBQUlSLHNCQUFzQixNQUFNO1FBQzlCLE9BQU9BLGtCQUFrQkssdUJBQXVCO0lBQ2xEO0lBQ0EsT0FBTztBQUNUO0FBRU8sU0FBUzdDLHVCQUNkaUQsSUFBWSxFQUNaQyxZQUE0QyxFQUM1Q0MsWUFBcUIsRUFDckJDLGVBQW9DO0lBRXBDLHlFQUF5RTtJQUN6RSxvRUFBb0U7SUFDcEUsTUFBTUMsTUFBTSxJQUFJQyxJQUFJQyxDQUFBQSxHQUFBQSxhQUFBQSxXQUFBQSxFQUFZTixPQUFPaEMsU0FBU2dDLElBQUk7SUFDcEQsSUFBSU8sS0FBd0MsRUFBRSxFQUU3QztJQUVESSxDQUFBQSxHQUFBQSxPQUFBQSwyQkFBQUEsRUFBNEJSO0lBRTVCLE1BQU1QLDBCQUEwQkc7SUFDaEMsSUFBSUgsNEJBQTRCLE1BQU07UUFDcENBLHdCQUF3QkksTUFBTUM7SUFDaEM7SUFFQVcsQ0FBQUEsR0FBQUEsZ0JBQUFBLHVCQUFBQSxFQUF3QjtRQUN0QmhELE1BQU15QixvQkFBQUEsZUFBZTtRQUNyQmU7UUFDQVMsZUFBZUMsQ0FBQUEsR0FBQUEsV0FBQUEsYUFBQUEsRUFBY1Y7UUFDN0JXLGdCQUFnQi9DLFNBQVNnRCxNQUFNO1FBQy9CZDtRQUNBRDtRQUNBZ0IsZUFBZTtJQUNqQjtBQUNGO0FBRU8sU0FBU2pFLHVCQUNkZ0QsSUFBWSxFQUNaa0IsSUFBbUM7SUFFbkMsTUFBTXRCLDBCQUEwQkc7SUFDaEMsSUFBSUgsNEJBQTRCLE1BQU07UUFDcENBLHdCQUF3QkksTUFBTTtJQUNoQztJQUNBWSxDQUFBQSxHQUFBQSxnQkFBQUEsdUJBQUFBLEVBQXdCO1FBQ3RCaEQsTUFBTW1CLG9CQUFBQSxjQUFjO1FBQ3BCcUIsS0FBSyxJQUFJQyxJQUFJTDtRQUNia0I7SUFDRjtBQUNGO0FBT08sTUFBTWhFLDBCQUE2QztJQUN4RGlFLE1BQU0sSUFBTXBELE9BQU9xRCxPQUFPLENBQUNELElBQUk7SUFDL0JFLFNBQVMsSUFBTXRELE9BQU9xRCxPQUFPLENBQUNDLE9BQU87SUFDckNDLFVBQVVmLE1BQXVDLEdBRTdDLGlEQUNpRDtJQUNqRCxDQVFDLEdBQ0QsQ0FBQ1AsTUFBY3dCO1FBQ2IsdUNBQXVDO1FBQ3ZDLE1BQU1wRSxjQUFjMEM7UUFDcEIsTUFBTU0sTUFBTTBCLENBQUFBLEdBQUFBLFdBQUFBLGlCQUFBQSxFQUFrQjlCO1FBQzlCLElBQUlJLFFBQVEsTUFBTTtnQkFVUm9CO1lBVFIsNERBQTREO1lBQzVELCtEQUErRDtZQUMvRCxpRUFBaUU7WUFDakUsZ0RBQWdEO1lBQ2hELG9EQUFvRDtZQUNwRCxrQkFBa0I7WUFDbEJPLENBQUFBLEdBQUFBLGlCQUFBQSxlQUFBQSxFQUFnQjNFLFlBQVljLEtBQUssRUFBRTtnQkFDakNOLE1BQU1vRSxvQkFBQUEsZUFBZTtnQkFDckI1QjtnQkFDQXVCLE1BQU1ILENBQUFBLGdCQUFBQSxXQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxRQUFTRyxJQUFBQSxLQUFJLE9BQWJILGdCQUFpQkksb0JBQUFBLFlBQVksQ0FBQ0MsSUFBSTtZQUMxQztRQUNGO0lBQ0Y7SUFDSkksU0FBUyxDQUFDakMsTUFBY3dCO1FBQ3RCdEMsQ0FBQUEsR0FBQUEsT0FBQUEsZUFBQUEsRUFBZ0I7Z0JBQzBCc0M7WUFBeEN6RSx1QkFBdUJpRCxNQUFNLFdBQVd3QixDQUFBQSxrQkFBQUEsV0FBQUEsT0FBQUEsS0FBQUEsSUFBQUEsUUFBU1UsTUFBQUEsS0FBTSxPQUFmVixrQkFBbUIsTUFBTTtRQUNuRTtJQUNGO0lBQ0FXLE1BQU0sQ0FBQ25DLE1BQWN3QjtRQUNuQnRDLENBQUFBLEdBQUFBLE9BQUFBLGVBQUFBLEVBQWdCO2dCQUN1QnNDO1lBQXJDekUsdUJBQXVCaUQsTUFBTSxRQUFRd0IsQ0FBQUEsa0JBQUFBLFdBQUFBLE9BQUFBLEtBQUFBLElBQUFBLFFBQVNVLE1BQU0sWUFBZlYsa0JBQW1CLE1BQU07UUFDaEU7SUFDRjtJQUNBWSxTQUFTO1FBQ1BsRCxDQUFBQSxHQUFBQSxPQUFBQSxlQUFBQSxFQUFnQjtZQUNkMEIsQ0FBQUEsR0FBQUEsZ0JBQUFBLHVCQUFBQSxFQUF3QjtnQkFDdEJoRCxNQUFNQyxvQkFBQUEsY0FBYztnQkFDcEJDLFFBQVFDLE9BQU9DLFFBQVEsQ0FBQ0YsTUFBTTtZQUNoQztRQUNGO0lBQ0Y7SUFDQXVFLFlBQVk7UUFDVixJQUFJOUIsS0FBb0IsRUFBb0IsRUFJM0MsTUFBTTtZQUNMckIsQ0FBQUEsR0FBQUEsT0FBQUEsZUFBQUEsRUFBZ0I7Z0JBQ2QwQixDQUFBQSxHQUFBQSxnQkFBQUEsdUJBQUFBLEVBQXdCO29CQUN0QmhELE1BQU0yRSxvQkFBQUEsa0JBQWtCO29CQUN4QnpFLFFBQVFDLE9BQU9DLFFBQVEsQ0FBQ0YsTUFBTTtnQkFDaEM7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLGdFQUFnRTtBQUNoRSxJQUFJLEtBQTZCLElBQUlDLE9BQU9SLElBQUksRUFBRTtJQUNoRFEsT0FBT1IsSUFBSSxDQUFDaUYsTUFBTSxHQUFHdEY7QUFDdkIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zcmMvY2xpZW50L2NvbXBvbmVudHMvYXBwLXJvdXRlci1pbnN0YW5jZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICB0eXBlIEFwcFJvdXRlclN0YXRlLFxuICB0eXBlIFJlZHVjZXJBY3Rpb25zLFxuICB0eXBlIFJlZHVjZXJTdGF0ZSxcbiAgQUNUSU9OX1JFRlJFU0gsXG4gIEFDVElPTl9TRVJWRVJfQUNUSU9OLFxuICBBQ1RJT05fTkFWSUdBVEUsXG4gIEFDVElPTl9SRVNUT1JFLFxuICB0eXBlIE5hdmlnYXRlQWN0aW9uLFxuICBBQ1RJT05fSE1SX1JFRlJFU0gsXG4gIFByZWZldGNoS2luZCxcbiAgQUNUSU9OX1BSRUZFVENILFxufSBmcm9tICcuL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzJ1xuaW1wb3J0IHsgcmVkdWNlciB9IGZyb20gJy4vcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXInXG5pbXBvcnQgeyBzdGFydFRyYW5zaXRpb24gfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IGlzVGhlbmFibGUgfSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2lzLXRoZW5hYmxlJ1xuaW1wb3J0IHsgcHJlZmV0Y2ggYXMgcHJlZmV0Y2hXaXRoU2VnbWVudENhY2hlIH0gZnJvbSAnLi9zZWdtZW50LWNhY2hlJ1xuaW1wb3J0IHsgZGlzcGF0Y2hBcHBSb3V0ZXJBY3Rpb24gfSBmcm9tICcuL3VzZS1hY3Rpb24tcXVldWUnXG5pbXBvcnQgeyBhZGRCYXNlUGF0aCB9IGZyb20gJy4uL2FkZC1iYXNlLXBhdGgnXG5pbXBvcnQgeyBjcmVhdGVQcmVmZXRjaFVSTCwgaXNFeHRlcm5hbFVSTCB9IGZyb20gJy4vYXBwLXJvdXRlcidcbmltcG9ydCB7IHByZWZldGNoUmVkdWNlciB9IGZyb20gJy4vcm91dGVyLXJlZHVjZXIvcmVkdWNlcnMvcHJlZmV0Y2gtcmVkdWNlcidcbmltcG9ydCB0eXBlIHtcbiAgQXBwUm91dGVySW5zdGFuY2UsXG4gIE5hdmlnYXRlT3B0aW9ucyxcbiAgUHJlZmV0Y2hPcHRpb25zLFxufSBmcm9tICcuLi8uLi9zaGFyZWQvbGliL2FwcC1yb3V0ZXItY29udGV4dC5zaGFyZWQtcnVudGltZSdcbmltcG9ydCB7IHNldExpbmtGb3JDdXJyZW50TmF2aWdhdGlvbiwgdHlwZSBMaW5rSW5zdGFuY2UgfSBmcm9tICcuL2xpbmtzJ1xuaW1wb3J0IHR5cGUgeyBGbGlnaHRSb3V0ZXJTdGF0ZSB9IGZyb20gJy4uLy4uL3NlcnZlci9hcHAtcmVuZGVyL3R5cGVzJ1xuaW1wb3J0IHR5cGUgeyBDbGllbnRJbnN0cnVtZW50YXRpb25Ib29rcyB9IGZyb20gJy4uL2FwcC1pbmRleCdcblxuZXhwb3J0IHR5cGUgRGlzcGF0Y2hTdGF0ZVByb21pc2UgPSBSZWFjdC5EaXNwYXRjaDxSZWR1Y2VyU3RhdGU+XG5cbmV4cG9ydCB0eXBlIEFwcFJvdXRlckFjdGlvblF1ZXVlID0ge1xuICBzdGF0ZTogQXBwUm91dGVyU3RhdGVcbiAgZGlzcGF0Y2g6IChwYXlsb2FkOiBSZWR1Y2VyQWN0aW9ucywgc2V0U3RhdGU6IERpc3BhdGNoU3RhdGVQcm9taXNlKSA9PiB2b2lkXG4gIGFjdGlvbjogKHN0YXRlOiBBcHBSb3V0ZXJTdGF0ZSwgYWN0aW9uOiBSZWR1Y2VyQWN0aW9ucykgPT4gUmVkdWNlclN0YXRlXG5cbiAgb25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnQ6XG4gICAgfCAoKHVybDogc3RyaW5nLCB0eXBlOiAncHVzaCcgfCAncmVwbGFjZScgfCAndHJhdmVyc2UnKSA9PiB2b2lkKVxuICAgIHwgbnVsbFxuXG4gIHBlbmRpbmc6IEFjdGlvblF1ZXVlTm9kZSB8IG51bGxcbiAgbmVlZHNSZWZyZXNoPzogYm9vbGVhblxuICBsYXN0OiBBY3Rpb25RdWV1ZU5vZGUgfCBudWxsXG59XG5cbmV4cG9ydCB0eXBlIEFjdGlvblF1ZXVlTm9kZSA9IHtcbiAgcGF5bG9hZDogUmVkdWNlckFjdGlvbnNcbiAgbmV4dDogQWN0aW9uUXVldWVOb2RlIHwgbnVsbFxuICByZXNvbHZlOiAodmFsdWU6IFJlZHVjZXJTdGF0ZSkgPT4gdm9pZFxuICByZWplY3Q6IChlcnI6IEVycm9yKSA9PiB2b2lkXG4gIGRpc2NhcmRlZD86IGJvb2xlYW5cbn1cblxuZnVuY3Rpb24gcnVuUmVtYWluaW5nQWN0aW9ucyhcbiAgYWN0aW9uUXVldWU6IEFwcFJvdXRlckFjdGlvblF1ZXVlLFxuICBzZXRTdGF0ZTogRGlzcGF0Y2hTdGF0ZVByb21pc2Vcbikge1xuICBpZiAoYWN0aW9uUXVldWUucGVuZGluZyAhPT0gbnVsbCkge1xuICAgIGFjdGlvblF1ZXVlLnBlbmRpbmcgPSBhY3Rpb25RdWV1ZS5wZW5kaW5nLm5leHRcbiAgICBpZiAoYWN0aW9uUXVldWUucGVuZGluZyAhPT0gbnVsbCkge1xuICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11c2UtYmVmb3JlLWRlZmluZVxuICAgICAgcnVuQWN0aW9uKHtcbiAgICAgICAgYWN0aW9uUXVldWUsXG4gICAgICAgIGFjdGlvbjogYWN0aW9uUXVldWUucGVuZGluZyxcbiAgICAgICAgc2V0U3RhdGUsXG4gICAgICB9KVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBObyBtb3JlIGFjdGlvbnMgYXJlIHBlbmRpbmcsIGNoZWNrIGlmIGEgcmVmcmVzaCBpcyBuZWVkZWRcbiAgICAgIGlmIChhY3Rpb25RdWV1ZS5uZWVkc1JlZnJlc2gpIHtcbiAgICAgICAgYWN0aW9uUXVldWUubmVlZHNSZWZyZXNoID0gZmFsc2VcbiAgICAgICAgYWN0aW9uUXVldWUuZGlzcGF0Y2goXG4gICAgICAgICAge1xuICAgICAgICAgICAgdHlwZTogQUNUSU9OX1JFRlJFU0gsXG4gICAgICAgICAgICBvcmlnaW46IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4sXG4gICAgICAgICAgfSxcbiAgICAgICAgICBzZXRTdGF0ZVxuICAgICAgICApXG4gICAgICB9XG4gICAgfVxuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIHJ1bkFjdGlvbih7XG4gIGFjdGlvblF1ZXVlLFxuICBhY3Rpb24sXG4gIHNldFN0YXRlLFxufToge1xuICBhY3Rpb25RdWV1ZTogQXBwUm91dGVyQWN0aW9uUXVldWVcbiAgYWN0aW9uOiBBY3Rpb25RdWV1ZU5vZGVcbiAgc2V0U3RhdGU6IERpc3BhdGNoU3RhdGVQcm9taXNlXG59KSB7XG4gIGNvbnN0IHByZXZTdGF0ZSA9IGFjdGlvblF1ZXVlLnN0YXRlXG5cbiAgYWN0aW9uUXVldWUucGVuZGluZyA9IGFjdGlvblxuXG4gIGNvbnN0IHBheWxvYWQgPSBhY3Rpb24ucGF5bG9hZFxuICBjb25zdCBhY3Rpb25SZXN1bHQgPSBhY3Rpb25RdWV1ZS5hY3Rpb24ocHJldlN0YXRlLCBwYXlsb2FkKVxuXG4gIGZ1bmN0aW9uIGhhbmRsZVJlc3VsdChuZXh0U3RhdGU6IEFwcFJvdXRlclN0YXRlKSB7XG4gICAgLy8gaWYgd2UgZGlzY2FyZGVkIHRoaXMgYWN0aW9uLCB0aGUgc3RhdGUgc2hvdWxkIGFsc28gYmUgZGlzY2FyZGVkXG4gICAgaWYgKGFjdGlvbi5kaXNjYXJkZWQpIHtcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGFjdGlvblF1ZXVlLnN0YXRlID0gbmV4dFN0YXRlXG5cbiAgICBydW5SZW1haW5pbmdBY3Rpb25zKGFjdGlvblF1ZXVlLCBzZXRTdGF0ZSlcbiAgICBhY3Rpb24ucmVzb2x2ZShuZXh0U3RhdGUpXG4gIH1cblxuICAvLyBpZiB0aGUgYWN0aW9uIGlzIGEgcHJvbWlzZSwgc2V0IHVwIGEgY2FsbGJhY2sgdG8gcmVzb2x2ZSBpdFxuICBpZiAoaXNUaGVuYWJsZShhY3Rpb25SZXN1bHQpKSB7XG4gICAgYWN0aW9uUmVzdWx0LnRoZW4oaGFuZGxlUmVzdWx0LCAoZXJyKSA9PiB7XG4gICAgICBydW5SZW1haW5pbmdBY3Rpb25zKGFjdGlvblF1ZXVlLCBzZXRTdGF0ZSlcbiAgICAgIGFjdGlvbi5yZWplY3QoZXJyKVxuICAgIH0pXG4gIH0gZWxzZSB7XG4gICAgaGFuZGxlUmVzdWx0KGFjdGlvblJlc3VsdClcbiAgfVxufVxuXG5mdW5jdGlvbiBkaXNwYXRjaEFjdGlvbihcbiAgYWN0aW9uUXVldWU6IEFwcFJvdXRlckFjdGlvblF1ZXVlLFxuICBwYXlsb2FkOiBSZWR1Y2VyQWN0aW9ucyxcbiAgc2V0U3RhdGU6IERpc3BhdGNoU3RhdGVQcm9taXNlXG4pIHtcbiAgbGV0IHJlc29sdmVyczoge1xuICAgIHJlc29sdmU6ICh2YWx1ZTogUmVkdWNlclN0YXRlKSA9PiB2b2lkXG4gICAgcmVqZWN0OiAocmVhc29uOiBhbnkpID0+IHZvaWRcbiAgfSA9IHsgcmVzb2x2ZTogc2V0U3RhdGUsIHJlamVjdDogKCkgPT4ge30gfVxuXG4gIC8vIG1vc3Qgb2YgdGhlIGFjdGlvbiB0eXBlcyBhcmUgYXN5bmMgd2l0aCB0aGUgZXhjZXB0aW9uIG9mIHJlc3RvcmVcbiAgLy8gaXQncyBpbXBvcnRhbnQgdGhhdCByZXN0b3JlIGlzIGhhbmRsZWQgcXVpY2tseSBzaW5jZSBpdCdzIGZpcmVkIG9uIHRoZSBwb3BzdGF0ZSBldmVudFxuICAvLyBhbmQgd2UgZG9uJ3Qgd2FudCB0byBhZGQgYW55IGRlbGF5IG9uIGEgYmFjay9mb3J3YXJkIG5hdlxuICAvLyB0aGlzIG9ubHkgY3JlYXRlcyBhIHByb21pc2UgZm9yIHRoZSBhc3luYyBhY3Rpb25zXG4gIGlmIChwYXlsb2FkLnR5cGUgIT09IEFDVElPTl9SRVNUT1JFKSB7XG4gICAgLy8gQ3JlYXRlIHRoZSBwcm9taXNlIGFuZCBhc3NpZ24gdGhlIHJlc29sdmVycyB0byB0aGUgb2JqZWN0LlxuICAgIGNvbnN0IGRlZmVycmVkUHJvbWlzZSA9IG5ldyBQcm9taXNlPEFwcFJvdXRlclN0YXRlPigocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICByZXNvbHZlcnMgPSB7IHJlc29sdmUsIHJlamVjdCB9XG4gICAgfSlcblxuICAgIHN0YXJ0VHJhbnNpdGlvbigoKSA9PiB7XG4gICAgICAvLyB3ZSBpbW1lZGlhdGVseSBub3RpZnkgUmVhY3Qgb2YgdGhlIHBlbmRpbmcgcHJvbWlzZSAtLSB0aGUgcmVzb2x2ZXIgaXMgYXR0YWNoZWQgdG8gdGhlIGFjdGlvbiBub2RlXG4gICAgICAvLyBhbmQgd2lsbCBiZSBjYWxsZWQgd2hlbiB0aGUgYXNzb2NpYXRlZCBhY3Rpb24gcHJvbWlzZSByZXNvbHZlc1xuICAgICAgc2V0U3RhdGUoZGVmZXJyZWRQcm9taXNlKVxuICAgIH0pXG4gIH1cblxuICBjb25zdCBuZXdBY3Rpb246IEFjdGlvblF1ZXVlTm9kZSA9IHtcbiAgICBwYXlsb2FkLFxuICAgIG5leHQ6IG51bGwsXG4gICAgcmVzb2x2ZTogcmVzb2x2ZXJzLnJlc29sdmUsXG4gICAgcmVqZWN0OiByZXNvbHZlcnMucmVqZWN0LFxuICB9XG5cbiAgLy8gQ2hlY2sgaWYgdGhlIHF1ZXVlIGlzIGVtcHR5XG4gIGlmIChhY3Rpb25RdWV1ZS5wZW5kaW5nID09PSBudWxsKSB7XG4gICAgLy8gVGhlIHF1ZXVlIGlzIGVtcHR5LCBzbyBhZGQgdGhlIGFjdGlvbiBhbmQgc3RhcnQgaXQgaW1tZWRpYXRlbHlcbiAgICAvLyBNYXJrIHRoaXMgYWN0aW9uIGFzIHRoZSBsYXN0IGluIHRoZSBxdWV1ZVxuICAgIGFjdGlvblF1ZXVlLmxhc3QgPSBuZXdBY3Rpb25cblxuICAgIHJ1bkFjdGlvbih7XG4gICAgICBhY3Rpb25RdWV1ZSxcbiAgICAgIGFjdGlvbjogbmV3QWN0aW9uLFxuICAgICAgc2V0U3RhdGUsXG4gICAgfSlcbiAgfSBlbHNlIGlmIChcbiAgICBwYXlsb2FkLnR5cGUgPT09IEFDVElPTl9OQVZJR0FURSB8fFxuICAgIHBheWxvYWQudHlwZSA9PT0gQUNUSU9OX1JFU1RPUkVcbiAgKSB7XG4gICAgLy8gTmF2aWdhdGlvbnMgKGluY2x1ZGluZyBiYWNrL2ZvcndhcmQpIHRha2UgcHJpb3JpdHkgb3ZlciBhbnkgcGVuZGluZyBhY3Rpb25zLlxuICAgIC8vIE1hcmsgdGhlIHBlbmRpbmcgYWN0aW9uIGFzIGRpc2NhcmRlZCAoc28gdGhlIHN0YXRlIGlzIG5ldmVyIGFwcGxpZWQpIGFuZCBzdGFydCB0aGUgbmF2aWdhdGlvbiBhY3Rpb24gaW1tZWRpYXRlbHkuXG4gICAgYWN0aW9uUXVldWUucGVuZGluZy5kaXNjYXJkZWQgPSB0cnVlXG5cbiAgICAvLyBUaGUgcmVzdCBvZiB0aGUgY3VycmVudCBxdWV1ZSBzaG91bGQgc3RpbGwgZXhlY3V0ZSBhZnRlciB0aGlzIG5hdmlnYXRpb24uXG4gICAgLy8gKE5vdGUgdGhhdCBpdCBjYW4ndCBjb250YWluIGFueSBlYXJsaWVyIG5hdmlnYXRpb25zLCBiZWNhdXNlIHdlIGFsd2F5cyBwdXQgdGhvc2UgaW50byBgYWN0aW9uUXVldWUucGVuZGluZ2AgYnkgY2FsbGluZyBgcnVuQWN0aW9uYClcbiAgICBuZXdBY3Rpb24ubmV4dCA9IGFjdGlvblF1ZXVlLnBlbmRpbmcubmV4dFxuXG4gICAgLy8gaWYgdGhlIHBlbmRpbmcgYWN0aW9uIHdhcyBhIHNlcnZlciBhY3Rpb24sIG1hcmsgdGhlIHF1ZXVlIGFzIG5lZWRpbmcgYSByZWZyZXNoIG9uY2UgZXZlbnRzIGFyZSBwcm9jZXNzZWRcbiAgICBpZiAoYWN0aW9uUXVldWUucGVuZGluZy5wYXlsb2FkLnR5cGUgPT09IEFDVElPTl9TRVJWRVJfQUNUSU9OKSB7XG4gICAgICBhY3Rpb25RdWV1ZS5uZWVkc1JlZnJlc2ggPSB0cnVlXG4gICAgfVxuXG4gICAgcnVuQWN0aW9uKHtcbiAgICAgIGFjdGlvblF1ZXVlLFxuICAgICAgYWN0aW9uOiBuZXdBY3Rpb24sXG4gICAgICBzZXRTdGF0ZSxcbiAgICB9KVxuICB9IGVsc2Uge1xuICAgIC8vIFRoZSBxdWV1ZSBpcyBub3QgZW1wdHksIHNvIGFkZCB0aGUgYWN0aW9uIHRvIHRoZSBlbmQgb2YgdGhlIHF1ZXVlXG4gICAgLy8gSXQgd2lsbCBiZSBzdGFydGVkIGJ5IHJ1blJlbWFpbmluZ0FjdGlvbnMgYWZ0ZXIgdGhlIHByZXZpb3VzIGFjdGlvbiBmaW5pc2hlc1xuICAgIGlmIChhY3Rpb25RdWV1ZS5sYXN0ICE9PSBudWxsKSB7XG4gICAgICBhY3Rpb25RdWV1ZS5sYXN0Lm5leHQgPSBuZXdBY3Rpb25cbiAgICB9XG4gICAgYWN0aW9uUXVldWUubGFzdCA9IG5ld0FjdGlvblxuICB9XG59XG5cbmxldCBnbG9iYWxBY3Rpb25RdWV1ZTogQXBwUm91dGVyQWN0aW9uUXVldWUgfCBudWxsID0gbnVsbFxuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlTXV0YWJsZUFjdGlvblF1ZXVlKFxuICBpbml0aWFsU3RhdGU6IEFwcFJvdXRlclN0YXRlLFxuICBpbnN0cnVtZW50YXRpb25Ib29rczogQ2xpZW50SW5zdHJ1bWVudGF0aW9uSG9va3MgfCBudWxsXG4pOiBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSB7XG4gIGNvbnN0IGFjdGlvblF1ZXVlOiBBcHBSb3V0ZXJBY3Rpb25RdWV1ZSA9IHtcbiAgICBzdGF0ZTogaW5pdGlhbFN0YXRlLFxuICAgIGRpc3BhdGNoOiAocGF5bG9hZDogUmVkdWNlckFjdGlvbnMsIHNldFN0YXRlOiBEaXNwYXRjaFN0YXRlUHJvbWlzZSkgPT5cbiAgICAgIGRpc3BhdGNoQWN0aW9uKGFjdGlvblF1ZXVlLCBwYXlsb2FkLCBzZXRTdGF0ZSksXG4gICAgYWN0aW9uOiBhc3luYyAoc3RhdGU6IEFwcFJvdXRlclN0YXRlLCBhY3Rpb246IFJlZHVjZXJBY3Rpb25zKSA9PiB7XG4gICAgICBjb25zdCByZXN1bHQgPSByZWR1Y2VyKHN0YXRlLCBhY3Rpb24pXG4gICAgICByZXR1cm4gcmVzdWx0XG4gICAgfSxcbiAgICBwZW5kaW5nOiBudWxsLFxuICAgIGxhc3Q6IG51bGwsXG4gICAgb25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnQ6XG4gICAgICBpbnN0cnVtZW50YXRpb25Ib29rcyAhPT0gbnVsbCAmJlxuICAgICAgdHlwZW9mIGluc3RydW1lbnRhdGlvbkhvb2tzLm9uUm91dGVyVHJhbnNpdGlvblN0YXJ0ID09PSAnZnVuY3Rpb24nXG4gICAgICAgID8gLy8gVGhpcyBwcm9maWxpbmcgaG9vayB3aWxsIGJlIGNhbGxlZCBhdCB0aGUgc3RhcnQgb2YgZXZlcnkgbmF2aWdhdGlvbi5cbiAgICAgICAgICBpbnN0cnVtZW50YXRpb25Ib29rcy5vblJvdXRlclRyYW5zaXRpb25TdGFydFxuICAgICAgICA6IG51bGwsXG4gIH1cblxuICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAvLyBUaGUgYWN0aW9uIHF1ZXVlIGlzIGxhemlseSBjcmVhdGVkIG9uIGh5ZHJhdGlvbiwgYnV0IGFmdGVyIHRoYXQgcG9pbnRcbiAgICAvLyBpdCBkb2Vzbid0IGNoYW5nZS4gU28gd2UgY2FuIHN0b3JlIGl0IGluIGEgZ2xvYmFsIHJhdGhlciB0aGFuIHBhc3NcbiAgICAvLyBpdCBhcm91bmQgZXZlcnl3aGVyZSB2aWEgcHJvcHMvY29udGV4dC5cbiAgICBpZiAoZ2xvYmFsQWN0aW9uUXVldWUgIT09IG51bGwpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ0ludGVybmFsIE5leHQuanMgRXJyb3I6IGNyZWF0ZU11dGFibGVBY3Rpb25RdWV1ZSB3YXMgY2FsbGVkIG1vcmUgJyArXG4gICAgICAgICAgJ3RoYW4gb25jZSdcbiAgICAgIClcbiAgICB9XG4gICAgZ2xvYmFsQWN0aW9uUXVldWUgPSBhY3Rpb25RdWV1ZVxuICB9XG5cbiAgcmV0dXJuIGFjdGlvblF1ZXVlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRDdXJyZW50QXBwUm91dGVyU3RhdGUoKTogQXBwUm91dGVyU3RhdGUgfCBudWxsIHtcbiAgcmV0dXJuIGdsb2JhbEFjdGlvblF1ZXVlICE9PSBudWxsID8gZ2xvYmFsQWN0aW9uUXVldWUuc3RhdGUgOiBudWxsXG59XG5cbmZ1bmN0aW9uIGdldEFwcFJvdXRlckFjdGlvblF1ZXVlKCk6IEFwcFJvdXRlckFjdGlvblF1ZXVlIHtcbiAgaWYgKGdsb2JhbEFjdGlvblF1ZXVlID09PSBudWxsKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgJ0ludGVybmFsIE5leHQuanMgZXJyb3I6IFJvdXRlciBhY3Rpb24gZGlzcGF0Y2hlZCBiZWZvcmUgaW5pdGlhbGl6YXRpb24uJ1xuICAgIClcbiAgfVxuICByZXR1cm4gZ2xvYmFsQWN0aW9uUXVldWVcbn1cblxuZnVuY3Rpb24gZ2V0UHJvZmlsaW5nSG9va0Zvck9uTmF2aWdhdGlvblN0YXJ0KCkge1xuICBpZiAoZ2xvYmFsQWN0aW9uUXVldWUgIT09IG51bGwpIHtcbiAgICByZXR1cm4gZ2xvYmFsQWN0aW9uUXVldWUub25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnRcbiAgfVxuICByZXR1cm4gbnVsbFxufVxuXG5leHBvcnQgZnVuY3Rpb24gZGlzcGF0Y2hOYXZpZ2F0ZUFjdGlvbihcbiAgaHJlZjogc3RyaW5nLFxuICBuYXZpZ2F0ZVR5cGU6IE5hdmlnYXRlQWN0aW9uWyduYXZpZ2F0ZVR5cGUnXSxcbiAgc2hvdWxkU2Nyb2xsOiBib29sZWFuLFxuICBsaW5rSW5zdGFuY2VSZWY6IExpbmtJbnN0YW5jZSB8IG51bGxcbik6IHZvaWQge1xuICAvLyBUT0RPOiBUaGlzIHN0dWZmIGNvdWxkIGp1c3QgZ28gaW50byB0aGUgcmVkdWNlci4gTGVhdmluZyBhcy1pcyBmb3Igbm93XG4gIC8vIHNpbmNlIHdlJ3JlIGFib3V0IHRvIHJld3JpdGUgYWxsIHRoZSByb3V0ZXIgcmVkdWNlciBzdHVmZiBhbnl3YXkuXG4gIGNvbnN0IHVybCA9IG5ldyBVUkwoYWRkQmFzZVBhdGgoaHJlZiksIGxvY2F0aW9uLmhyZWYpXG4gIGlmIChwcm9jZXNzLmVudi5fX05FWFRfQVBQX05BVl9GQUlMX0hBTkRMSU5HKSB7XG4gICAgd2luZG93Lm5leHQuX19wZW5kaW5nVXJsID0gdXJsXG4gIH1cblxuICBzZXRMaW5rRm9yQ3VycmVudE5hdmlnYXRpb24obGlua0luc3RhbmNlUmVmKVxuXG4gIGNvbnN0IG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0ID0gZ2V0UHJvZmlsaW5nSG9va0Zvck9uTmF2aWdhdGlvblN0YXJ0KClcbiAgaWYgKG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0ICE9PSBudWxsKSB7XG4gICAgb25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnQoaHJlZiwgbmF2aWdhdGVUeXBlKVxuICB9XG5cbiAgZGlzcGF0Y2hBcHBSb3V0ZXJBY3Rpb24oe1xuICAgIHR5cGU6IEFDVElPTl9OQVZJR0FURSxcbiAgICB1cmwsXG4gICAgaXNFeHRlcm5hbFVybDogaXNFeHRlcm5hbFVSTCh1cmwpLFxuICAgIGxvY2F0aW9uU2VhcmNoOiBsb2NhdGlvbi5zZWFyY2gsXG4gICAgc2hvdWxkU2Nyb2xsLFxuICAgIG5hdmlnYXRlVHlwZSxcbiAgICBhbGxvd0FsaWFzaW5nOiB0cnVlLFxuICB9KVxufVxuXG5leHBvcnQgZnVuY3Rpb24gZGlzcGF0Y2hUcmF2ZXJzZUFjdGlvbihcbiAgaHJlZjogc3RyaW5nLFxuICB0cmVlOiBGbGlnaHRSb3V0ZXJTdGF0ZSB8IHVuZGVmaW5lZFxuKSB7XG4gIGNvbnN0IG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0ID0gZ2V0UHJvZmlsaW5nSG9va0Zvck9uTmF2aWdhdGlvblN0YXJ0KClcbiAgaWYgKG9uUm91dGVyVHJhbnNpdGlvblN0YXJ0ICE9PSBudWxsKSB7XG4gICAgb25Sb3V0ZXJUcmFuc2l0aW9uU3RhcnQoaHJlZiwgJ3RyYXZlcnNlJylcbiAgfVxuICBkaXNwYXRjaEFwcFJvdXRlckFjdGlvbih7XG4gICAgdHlwZTogQUNUSU9OX1JFU1RPUkUsXG4gICAgdXJsOiBuZXcgVVJMKGhyZWYpLFxuICAgIHRyZWUsXG4gIH0pXG59XG5cbi8qKlxuICogVGhlIGFwcCByb3V0ZXIgdGhhdCBpcyBleHBvc2VkIHRocm91Z2ggYHVzZVJvdXRlcmAuIFRoZXNlIGFyZSBwdWJsaWMgQVBJXG4gKiBtZXRob2RzLiBJbnRlcm5hbCBOZXh0LmpzIGNvZGUgc2hvdWxkIGNhbGwgdGhlIGxvd2VyIGxldmVsIG1ldGhvZHMgZGlyZWN0bHlcbiAqIChhbHRob3VnaCB0aGVyZSdzIGxvdHMgb2YgZXhpc3RpbmcgY29kZSB0aGF0IGRvZXNuJ3QgZG8gdGhhdCkuXG4gKi9cbmV4cG9ydCBjb25zdCBwdWJsaWNBcHBSb3V0ZXJJbnN0YW5jZTogQXBwUm91dGVySW5zdGFuY2UgPSB7XG4gIGJhY2s6ICgpID0+IHdpbmRvdy5oaXN0b3J5LmJhY2soKSxcbiAgZm9yd2FyZDogKCkgPT4gd2luZG93Lmhpc3RvcnkuZm9yd2FyZCgpLFxuICBwcmVmZXRjaDogcHJvY2Vzcy5lbnYuX19ORVhUX0NMSUVOVF9TRUdNRU5UX0NBQ0hFXG4gICAgPyAvLyBVbmxpa2UgdGhlIG9sZCBpbXBsZW1lbnRhdGlvbiwgdGhlIFNlZ21lbnQgQ2FjaGUgZG9lc24ndCBzdG9yZSBpdHNcbiAgICAgIC8vIGRhdGEgaW4gdGhlIHJvdXRlciByZWR1Y2VyIHN0YXRlOyBpdCB3cml0ZXMgaW50byBhIGdsb2JhbCBtdXRhYmxlXG4gICAgICAvLyBjYWNoZS4gU28gd2UgZG9uJ3QgbmVlZCB0byBkaXNwYXRjaCBhbiBhY3Rpb24uXG4gICAgICAoaHJlZjogc3RyaW5nLCBvcHRpb25zPzogUHJlZmV0Y2hPcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IGFjdGlvblF1ZXVlID0gZ2V0QXBwUm91dGVyQWN0aW9uUXVldWUoKVxuICAgICAgICBwcmVmZXRjaFdpdGhTZWdtZW50Q2FjaGUoXG4gICAgICAgICAgaHJlZixcbiAgICAgICAgICBhY3Rpb25RdWV1ZS5zdGF0ZS5uZXh0VXJsLFxuICAgICAgICAgIGFjdGlvblF1ZXVlLnN0YXRlLnRyZWUsXG4gICAgICAgICAgb3B0aW9ucz8ua2luZCA9PT0gUHJlZmV0Y2hLaW5kLkZVTExcbiAgICAgICAgKVxuICAgICAgfVxuICAgIDogKGhyZWY6IHN0cmluZywgb3B0aW9ucz86IFByZWZldGNoT3B0aW9ucykgPT4ge1xuICAgICAgICAvLyBVc2UgdGhlIG9sZCBwcmVmZXRjaCBpbXBsZW1lbnRhdGlvbi5cbiAgICAgICAgY29uc3QgYWN0aW9uUXVldWUgPSBnZXRBcHBSb3V0ZXJBY3Rpb25RdWV1ZSgpXG4gICAgICAgIGNvbnN0IHVybCA9IGNyZWF0ZVByZWZldGNoVVJMKGhyZWYpXG4gICAgICAgIGlmICh1cmwgIT09IG51bGwpIHtcbiAgICAgICAgICAvLyBUaGUgcHJlZmV0Y2ggcmVkdWNlciBkb2Vzbid0IGFjdHVhbGx5IHVwZGF0ZSBhbnkgc3RhdGUgb3JcbiAgICAgICAgICAvLyB0cmlnZ2VyIGEgcmVyZW5kZXIuIEl0IGp1c3Qgd3JpdGVzIHRvIGEgbXV0YWJsZSBjYWNoZS4gU28gd2VcbiAgICAgICAgICAvLyBzaG91bGRuJ3QgYm90aGVyIGNhbGxpbmcgc2V0U3RhdGUvZGlzcGF0Y2g7IHdlIGNhbiBqdXN0IHJlLXJ1blxuICAgICAgICAgIC8vIHRoZSByZWR1Y2VyIGRpcmVjdGx5IHVzaW5nIHRoZSBjdXJyZW50IHN0YXRlLlxuICAgICAgICAgIC8vIFRPRE86IFJlZmFjdG9yIHRoaXMgYXdheSBmcm9tIGEgXCJyZWR1Y2VyXCIgc28gaXQnc1xuICAgICAgICAgIC8vIGxlc3MgY29uZnVzaW5nLlxuICAgICAgICAgIHByZWZldGNoUmVkdWNlcihhY3Rpb25RdWV1ZS5zdGF0ZSwge1xuICAgICAgICAgICAgdHlwZTogQUNUSU9OX1BSRUZFVENILFxuICAgICAgICAgICAgdXJsLFxuICAgICAgICAgICAga2luZDogb3B0aW9ucz8ua2luZCA/PyBQcmVmZXRjaEtpbmQuRlVMTCxcbiAgICAgICAgICB9KVxuICAgICAgICB9XG4gICAgICB9LFxuICByZXBsYWNlOiAoaHJlZjogc3RyaW5nLCBvcHRpb25zPzogTmF2aWdhdGVPcHRpb25zKSA9PiB7XG4gICAgc3RhcnRUcmFuc2l0aW9uKCgpID0+IHtcbiAgICAgIGRpc3BhdGNoTmF2aWdhdGVBY3Rpb24oaHJlZiwgJ3JlcGxhY2UnLCBvcHRpb25zPy5zY3JvbGwgPz8gdHJ1ZSwgbnVsbClcbiAgICB9KVxuICB9LFxuICBwdXNoOiAoaHJlZjogc3RyaW5nLCBvcHRpb25zPzogTmF2aWdhdGVPcHRpb25zKSA9PiB7XG4gICAgc3RhcnRUcmFuc2l0aW9uKCgpID0+IHtcbiAgICAgIGRpc3BhdGNoTmF2aWdhdGVBY3Rpb24oaHJlZiwgJ3B1c2gnLCBvcHRpb25zPy5zY3JvbGwgPz8gdHJ1ZSwgbnVsbClcbiAgICB9KVxuICB9LFxuICByZWZyZXNoOiAoKSA9PiB7XG4gICAgc3RhcnRUcmFuc2l0aW9uKCgpID0+IHtcbiAgICAgIGRpc3BhdGNoQXBwUm91dGVyQWN0aW9uKHtcbiAgICAgICAgdHlwZTogQUNUSU9OX1JFRlJFU0gsXG4gICAgICAgIG9yaWdpbjogd2luZG93LmxvY2F0aW9uLm9yaWdpbixcbiAgICAgIH0pXG4gICAgfSlcbiAgfSxcbiAgaG1yUmVmcmVzaDogKCkgPT4ge1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAnaG1yUmVmcmVzaCBjYW4gb25seSBiZSB1c2VkIGluIGRldmVsb3BtZW50IG1vZGUuIFBsZWFzZSB1c2UgcmVmcmVzaCBpbnN0ZWFkLidcbiAgICAgIClcbiAgICB9IGVsc2Uge1xuICAgICAgc3RhcnRUcmFuc2l0aW9uKCgpID0+IHtcbiAgICAgICAgZGlzcGF0Y2hBcHBSb3V0ZXJBY3Rpb24oe1xuICAgICAgICAgIHR5cGU6IEFDVElPTl9ITVJfUkVGUkVTSCxcbiAgICAgICAgICBvcmlnaW46IHdpbmRvdy5sb2NhdGlvbi5vcmlnaW4sXG4gICAgICAgIH0pXG4gICAgICB9KVxuICAgIH1cbiAgfSxcbn1cblxuLy8gRXhpc3RzIGZvciBkZWJ1Z2dpbmcgcHVycG9zZXMuIERvbid0IHVzZSBpbiBhcHBsaWNhdGlvbiBjb2RlLlxuaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5uZXh0KSB7XG4gIHdpbmRvdy5uZXh0LnJvdXRlciA9IHB1YmxpY0FwcFJvdXRlckluc3RhbmNlXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlTXV0YWJsZUFjdGlvblF1ZXVlIiwiZGlzcGF0Y2hOYXZpZ2F0ZUFjdGlvbiIsImRpc3BhdGNoVHJhdmVyc2VBY3Rpb24iLCJnZXRDdXJyZW50QXBwUm91dGVyU3RhdGUiLCJwdWJsaWNBcHBSb3V0ZXJJbnN0YW5jZSIsInJ1blJlbWFpbmluZ0FjdGlvbnMiLCJhY3Rpb25RdWV1ZSIsInNldFN0YXRlIiwicGVuZGluZyIsIm5leHQiLCJydW5BY3Rpb24iLCJhY3Rpb24iLCJuZWVkc1JlZnJlc2giLCJkaXNwYXRjaCIsInR5cGUiLCJBQ1RJT05fUkVGUkVTSCIsIm9yaWdpbiIsIndpbmRvdyIsImxvY2F0aW9uIiwicHJldlN0YXRlIiwic3RhdGUiLCJwYXlsb2FkIiwiYWN0aW9uUmVzdWx0IiwiaGFuZGxlUmVzdWx0IiwibmV4dFN0YXRlIiwiZGlzY2FyZGVkIiwicmVzb2x2ZSIsImlzVGhlbmFibGUiLCJ0aGVuIiwiZXJyIiwicmVqZWN0IiwiZGlzcGF0Y2hBY3Rpb24iLCJyZXNvbHZlcnMiLCJBQ1RJT05fUkVTVE9SRSIsImRlZmVycmVkUHJvbWlzZSIsIlByb21pc2UiLCJzdGFydFRyYW5zaXRpb24iLCJuZXdBY3Rpb24iLCJsYXN0IiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1NFUlZFUl9BQ1RJT04iLCJnbG9iYWxBY3Rpb25RdWV1ZSIsImluaXRpYWxTdGF0ZSIsImluc3RydW1lbnRhdGlvbkhvb2tzIiwicmVzdWx0IiwicmVkdWNlciIsIm9uUm91dGVyVHJhbnNpdGlvblN0YXJ0IiwiRXJyb3IiLCJnZXRBcHBSb3V0ZXJBY3Rpb25RdWV1ZSIsImdldFByb2ZpbGluZ0hvb2tGb3JPbk5hdmlnYXRpb25TdGFydCIsImhyZWYiLCJuYXZpZ2F0ZVR5cGUiLCJzaG91bGRTY3JvbGwiLCJsaW5rSW5zdGFuY2VSZWYiLCJ1cmwiLCJVUkwiLCJhZGRCYXNlUGF0aCIsInByb2Nlc3MiLCJlbnYiLCJfX05FWFRfQVBQX05BVl9GQUlMX0hBTkRMSU5HIiwiX19wZW5kaW5nVXJsIiwic2V0TGlua0ZvckN1cnJlbnROYXZpZ2F0aW9uIiwiZGlzcGF0Y2hBcHBSb3V0ZXJBY3Rpb24iLCJpc0V4dGVybmFsVXJsIiwiaXNFeHRlcm5hbFVSTCIsImxvY2F0aW9uU2VhcmNoIiwic2VhcmNoIiwiYWxsb3dBbGlhc2luZyIsInRyZWUiLCJiYWNrIiwiaGlzdG9yeSIsImZvcndhcmQiLCJwcmVmZXRjaCIsIl9fTkVYVF9DTElFTlRfU0VHTUVOVF9DQUNIRSIsIm9wdGlvbnMiLCJwcmVmZXRjaFdpdGhTZWdtZW50Q2FjaGUiLCJuZXh0VXJsIiwia2luZCIsIlByZWZldGNoS2luZCIsIkZVTEwiLCJjcmVhdGVQcmVmZXRjaFVSTCIsInByZWZldGNoUmVkdWNlciIsIkFDVElPTl9QUkVGRVRDSCIsInJlcGxhY2UiLCJzY3JvbGwiLCJwdXNoIiwicmVmcmVzaCIsImhtclJlZnJlc2giLCJOT0RFX0VOViIsIkFDVElPTl9ITVJfUkVGUkVTSCIsInJvdXRlciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/components/app-router.js ***!
  \****************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createEmptyCacheNode: function() {\n        return createEmptyCacheNode;\n    },\n    createPrefetchURL: function() {\n        return createPrefetchURL;\n    },\n    default: function() {\n        return AppRouter;\n    },\n    isExternalURL: function() {\n        return isExternalURL;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _createhreffromurl = __webpack_require__(/*! ./router-reducer/create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _hooksclientcontextsharedruntime = __webpack_require__(/*! ../../shared/lib/hooks-client-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\");\nconst _useactionqueue = __webpack_require__(/*! ./use-action-queue */ \"(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\");\nconst _errorboundary = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! ./error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\"));\nconst _isbot = __webpack_require__(/*! ../../shared/lib/router/utils/is-bot */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-bot.js\");\nconst _addbasepath = __webpack_require__(/*! ../add-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _approuterannouncer = __webpack_require__(/*! ./app-router-announcer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-announcer.js\");\nconst _redirectboundary = __webpack_require__(/*! ./redirect-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-boundary.js\");\nconst _findheadincache = __webpack_require__(/*! ./router-reducer/reducers/find-head-in-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/find-head-in-cache.js\");\nconst _unresolvedthenable = __webpack_require__(/*! ./unresolved-thenable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/unresolved-thenable.js\");\nconst _removebasepath = __webpack_require__(/*! ../remove-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/remove-base-path.js\");\nconst _hasbasepath = __webpack_require__(/*! ../has-base-path */ \"(app-pages-browser)/./node_modules/next/dist/client/has-base-path.js\");\nconst _computechangedpath = __webpack_require__(/*! ./router-reducer/compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _navfailurehandler = __webpack_require__(/*! ./nav-failure-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/nav-failure-handler.js\");\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _redirect = __webpack_require__(/*! ./redirect */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect.js\");\nconst _redirecterror = __webpack_require__(/*! ./redirect-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/redirect-error.js\");\nconst _links = __webpack_require__(/*! ./links */ \"(app-pages-browser)/./node_modules/next/dist/client/components/links.js\");\nconst globalMutable = {};\nfunction isExternalURL(url) {\n    return url.origin !== window.location.origin;\n}\nfunction createPrefetchURL(href) {\n    // Don't prefetch for bots as they don't navigate.\n    if ((0, _isbot.isBot)(window.navigator.userAgent)) {\n        return null;\n    }\n    let url;\n    try {\n        url = new URL((0, _addbasepath.addBasePath)(href), window.location.href);\n    } catch (_) {\n        // TODO: Does this need to throw or can we just console.error instead? Does\n        // anyone rely on this throwing? (Seems unlikely.)\n        throw Object.defineProperty(new Error(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\"), \"__NEXT_ERROR_CODE\", {\n            value: \"E234\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    // Don't prefetch during development (improves compilation performance)\n    if (true) {\n        return null;\n    }\n    // External urls can't be prefetched in the same way.\n    if (isExternalURL(url)) {\n        return null;\n    }\n    return url;\n}\nfunction HistoryUpdater(param) {\n    let { appRouterState } = param;\n    (0, _react.useInsertionEffect)(()=>{\n        if (false) {}\n        const { tree, pushRef, canonicalUrl } = appRouterState;\n        const historyState = {\n            ...pushRef.preserveCustomHistoryState ? window.history.state : {},\n            // Identifier is shortened intentionally.\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            // __N is used to identify if the history entry can be handled by the old router.\n            __NA: true,\n            __PRIVATE_NEXTJS_INTERNALS_TREE: tree\n        };\n        if (pushRef.pendingPush && // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n        // This mirrors the browser behavior for normal navigation.\n        (0, _createhreffromurl.createHrefFromUrl)(new URL(window.location.href)) !== canonicalUrl) {\n            // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n            pushRef.pendingPush = false;\n            window.history.pushState(historyState, '', canonicalUrl);\n        } else {\n            window.history.replaceState(historyState, '', canonicalUrl);\n        }\n    }, [\n        appRouterState\n    ]);\n    (0, _react.useEffect)(()=>{\n        // The Next-Url and the base tree may affect the result of a prefetch\n        // task. Re-prefetch all visible links with the updated values. In most\n        // cases, this will not result in any new network requests, only if\n        // the prefetch result actually varies on one of these inputs.\n        if (false) {}\n    }, [\n        appRouterState.nextUrl,\n        appRouterState.tree\n    ]);\n    return null;\n}\n_c = HistoryUpdater;\nfunction createEmptyCacheNode() {\n    return {\n        lazyData: null,\n        rsc: null,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        parallelRoutes: new Map(),\n        loading: null,\n        navigatedAt: -1\n    };\n}\nfunction copyNextJsInternalHistoryState(data) {\n    if (data == null) data = {};\n    const currentState = window.history.state;\n    const __NA = currentState == null ? void 0 : currentState.__NA;\n    if (__NA) {\n        data.__NA = __NA;\n    }\n    const __PRIVATE_NEXTJS_INTERNALS_TREE = currentState == null ? void 0 : currentState.__PRIVATE_NEXTJS_INTERNALS_TREE;\n    if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n        data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE;\n    }\n    return data;\n}\nfunction Head(param) {\n    let { headCacheNode } = param;\n    // If this segment has a `prefetchHead`, it's the statically prefetched data.\n    // We should use that on initial render instead of `head`. Then we'll switch\n    // to `head` when the dynamic response streams in.\n    const head = headCacheNode !== null ? headCacheNode.head : null;\n    const prefetchHead = headCacheNode !== null ? headCacheNode.prefetchHead : null;\n    // If no prefetch data is available, then we go straight to rendering `head`.\n    const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head;\n    // We use `useDeferredValue` to handle switching between the prefetched and\n    // final values. The second argument is returned on initial render, then it\n    // re-renders with the first argument.\n    return (0, _react.useDeferredValue)(head, resolvedPrefetchRsc);\n}\n_c1 = Head;\n/**\n * The global router that wraps the application components.\n */ function Router(param) {\n    let { actionQueue, assetPrefix, globalError } = param;\n    const state = (0, _useactionqueue.useActionQueue)(actionQueue);\n    const { canonicalUrl } = state;\n    // Add memoized pathname/query for useSearchParams and usePathname.\n    const { searchParams, pathname } = (0, _react.useMemo)(()=>{\n        const url = new URL(canonicalUrl,  false ? 0 : window.location.href);\n        return {\n            // This is turned into a readonly class in `useSearchParams`\n            searchParams: url.searchParams,\n            pathname: (0, _hasbasepath.hasBasePath)(url.pathname) ? (0, _removebasepath.removeBasePath)(url.pathname) : url.pathname\n        };\n    }, [\n        canonicalUrl\n    ]);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const { cache, prefetchCache, tree } = state;\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            // Add `window.nd` for debugging purposes.\n            // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n            // @ts-ignore this is for debugging\n            window.nd = {\n                router: _approuterinstance.publicAppRouterInstance,\n                cache,\n                prefetchCache,\n                tree\n            };\n        }, [\n            cache,\n            prefetchCache,\n            tree\n        ]);\n    }\n    (0, _react.useEffect)(()=>{\n        // If the app is restored from bfcache, it's possible that\n        // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n        // would trigger the mpa navigation logic again from the lines below.\n        // This will restore the router to the initial state in the event that the app is restored from bfcache.\n        function handlePageShow(event) {\n            var _window_history_state;\n            if (!event.persisted || !((_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE)) {\n                return;\n            }\n            // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n            // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n            // of the last MPA navigation.\n            globalMutable.pendingMpaPath = undefined;\n            (0, _useactionqueue.dispatchAppRouterAction)({\n                type: _routerreducertypes.ACTION_RESTORE,\n                url: new URL(window.location.href),\n                tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE\n            });\n        }\n        window.addEventListener('pageshow', handlePageShow);\n        return ()=>{\n            window.removeEventListener('pageshow', handlePageShow);\n        };\n    }, []);\n    (0, _react.useEffect)(()=>{\n        // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n        // are caught and handled by the router.\n        function handleUnhandledRedirect(event) {\n            const error = 'reason' in event ? event.reason : event.error;\n            if ((0, _redirecterror.isRedirectError)(error)) {\n                event.preventDefault();\n                const url = (0, _redirect.getURLFromRedirectError)(error);\n                const redirectType = (0, _redirect.getRedirectTypeFromError)(error);\n                // TODO: This should access the router methods directly, rather than\n                // go through the public interface.\n                if (redirectType === _redirecterror.RedirectType.push) {\n                    _approuterinstance.publicAppRouterInstance.push(url, {});\n                } else {\n                    _approuterinstance.publicAppRouterInstance.replace(url, {});\n                }\n            }\n        }\n        window.addEventListener('error', handleUnhandledRedirect);\n        window.addEventListener('unhandledrejection', handleUnhandledRedirect);\n        return ()=>{\n            window.removeEventListener('error', handleUnhandledRedirect);\n            window.removeEventListener('unhandledrejection', handleUnhandledRedirect);\n        };\n    }, []);\n    // When mpaNavigation flag is set do a hard navigation to the new url.\n    // Infinitely suspend because we don't actually want to rerender any child\n    // components with the new URL and any entangled state updates shouldn't\n    // commit either (eg: useTransition isPending should stay true until the page\n    // unloads).\n    //\n    // This is a side effect in render. Don't try this at home, kids. It's\n    // probably safe because we know this is a singleton component and it's never\n    // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n    // but that's... fine?)\n    const { pushRef } = state;\n    if (pushRef.mpaNavigation) {\n        // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n        if (globalMutable.pendingMpaPath !== canonicalUrl) {\n            const location = window.location;\n            if (pushRef.pendingPush) {\n                location.assign(canonicalUrl);\n            } else {\n                location.replace(canonicalUrl);\n            }\n            globalMutable.pendingMpaPath = canonicalUrl;\n        }\n        // TODO-APP: Should we listen to navigateerror here to catch failed\n        // navigations somehow? And should we call window.stop() if a SPA navigation\n        // should interrupt an MPA one?\n        (0, _react.use)(_unresolvedthenable.unresolvedThenable);\n    }\n    (0, _react.useEffect)(()=>{\n        const originalPushState = window.history.pushState.bind(window.history);\n        const originalReplaceState = window.history.replaceState.bind(window.history);\n        // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n        const applyUrlFromHistoryPushReplace = (url)=>{\n            var _window_history_state;\n            const href = window.location.href;\n            const tree = (_window_history_state = window.history.state) == null ? void 0 : _window_history_state.__PRIVATE_NEXTJS_INTERNALS_TREE;\n            (0, _react.startTransition)(()=>{\n                (0, _useactionqueue.dispatchAppRouterAction)({\n                    type: _routerreducertypes.ACTION_RESTORE,\n                    url: new URL(url != null ? url : href, href),\n                    tree\n                });\n            });\n        };\n        /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.pushState = function pushState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalPushState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalPushState(data, _unused, url);\n        };\n        /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */ window.history.replaceState = function replaceState(data, _unused, url) {\n            // Avoid a loop when Next.js internals trigger pushState/replaceState\n            if ((data == null ? void 0 : data.__NA) || (data == null ? void 0 : data._N)) {\n                return originalReplaceState(data, _unused, url);\n            }\n            data = copyNextJsInternalHistoryState(data);\n            if (url) {\n                applyUrlFromHistoryPushReplace(url);\n            }\n            return originalReplaceState(data, _unused, url);\n        };\n        /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */ const onPopState = (event)=>{\n            if (!event.state) {\n                // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n                return;\n            }\n            // This case happens when the history entry was pushed by the `pages` router.\n            if (!event.state.__NA) {\n                window.location.reload();\n                return;\n            }\n            // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n            // Without startTransition works if the cache is there for this path\n            (0, _react.startTransition)(()=>{\n                (0, _approuterinstance.dispatchTraverseAction)(window.location.href, event.state.__PRIVATE_NEXTJS_INTERNALS_TREE);\n            });\n        };\n        // Register popstate event to call onPopstate.\n        window.addEventListener('popstate', onPopState);\n        return ()=>{\n            window.history.pushState = originalPushState;\n            window.history.replaceState = originalReplaceState;\n            window.removeEventListener('popstate', onPopState);\n        };\n    }, []);\n    const { cache, tree, nextUrl, focusAndScrollRef } = state;\n    const matchingHead = (0, _react.useMemo)(()=>{\n        return (0, _findheadincache.findHeadInCache)(cache, tree[1]);\n    }, [\n        cache,\n        tree\n    ]);\n    // Add memoized pathParams for useParams.\n    const pathParams = (0, _react.useMemo)(()=>{\n        return (0, _computechangedpath.getSelectedParams)(tree);\n    }, [\n        tree\n    ]);\n    const layoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            parentTree: tree,\n            parentCacheNode: cache,\n            parentSegmentPath: null,\n            // Root node always has `url`\n            // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n            url: canonicalUrl\n        };\n    }, [\n        tree,\n        cache,\n        canonicalUrl\n    ]);\n    const globalLayoutRouterContext = (0, _react.useMemo)(()=>{\n        return {\n            tree,\n            focusAndScrollRef,\n            nextUrl\n        };\n    }, [\n        tree,\n        focusAndScrollRef,\n        nextUrl\n    ]);\n    let head;\n    if (matchingHead !== null) {\n        // The head is wrapped in an extra component so we can use\n        // `useDeferredValue` to swap between the prefetched and final versions of\n        // the head. (This is what LayoutRouter does for segment data, too.)\n        //\n        // The `key` is used to remount the component whenever the head moves to\n        // a different segment.\n        const [headCacheNode, headKey] = matchingHead;\n        head = /*#__PURE__*/ (0, _jsxruntime.jsx)(Head, {\n            headCacheNode: headCacheNode\n        }, headKey);\n    } else {\n        head = null;\n    }\n    let content = /*#__PURE__*/ (0, _jsxruntime.jsxs)(_redirectboundary.RedirectBoundary, {\n        children: [\n            head,\n            cache.rsc,\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_approuterannouncer.AppRouterAnnouncer, {\n                tree: tree\n            })\n        ]\n    });\n    if (true) {\n        // In development, we apply few error boundaries and hot-reloader:\n        // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n        // - HotReloader:\n        //  - hot-reload the app when the code changes\n        //  - render dev overlay\n        //  - catch runtime errors and display global-error when necessary\n        if (true) {\n            const { DevRootHTTPAccessFallbackBoundary } = __webpack_require__(/*! ./dev-root-http-access-fallback-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/dev-root-http-access-fallback-boundary.js\");\n            content = /*#__PURE__*/ (0, _jsxruntime.jsx)(DevRootHTTPAccessFallbackBoundary, {\n                children: content\n            });\n        }\n        const HotReloader = (__webpack_require__(/*! ./react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\")[\"default\"]);\n        content = /*#__PURE__*/ (0, _jsxruntime.jsx)(HotReloader, {\n            assetPrefix: assetPrefix,\n            globalError: globalError,\n            children: content\n        });\n    } else {}\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(HistoryUpdater, {\n                appRouterState: state\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(RuntimeStyles, {}),\n            /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathParamsContext.Provider, {\n                value: pathParams,\n                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.PathnameContext.Provider, {\n                    value: pathname,\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_hooksclientcontextsharedruntime.SearchParamsContext.Provider, {\n                        value: searchParams,\n                        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.GlobalLayoutRouterContext.Provider, {\n                            value: globalLayoutRouterContext,\n                            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.AppRouterContext.Provider, {\n                                value: _approuterinstance.publicAppRouterInstance,\n                                children: /*#__PURE__*/ (0, _jsxruntime.jsx)(_approutercontextsharedruntime.LayoutRouterContext.Provider, {\n                                    value: layoutRouterContext,\n                                    children: content\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        ]\n    });\n}\n_c2 = Router;\nfunction AppRouter(param) {\n    let { actionQueue, globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles], assetPrefix } = param;\n    (0, _navfailurehandler.useNavFailureHandler)();\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_errorboundary.ErrorBoundary, {\n        // At the very top level, use the default GlobalError component as the final fallback.\n        // When the app router itself fails, which means the framework itself fails, we show the default error.\n        errorComponent: _errorboundary.default,\n        children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Router, {\n            actionQueue: actionQueue,\n            assetPrefix: assetPrefix,\n            globalError: [\n                globalErrorComponent,\n                globalErrorStyles\n            ]\n        })\n    });\n}\n_c3 = AppRouter;\nconst runtimeStyles = new Set();\nlet runtimeStyleChanged = new Set();\nglobalThis._N_E_STYLE_LOAD = function(href) {\n    let len = runtimeStyles.size;\n    runtimeStyles.add(href);\n    if (runtimeStyles.size !== len) {\n        runtimeStyleChanged.forEach((cb)=>cb());\n    }\n    // TODO figure out how to get a promise here\n    // But maybe it's not necessary as react would block rendering until it's loaded\n    return Promise.resolve();\n};\nfunction RuntimeStyles() {\n    _s();\n    const [, forceUpdate] = _react.default.useState(0);\n    const renderedStylesSize = runtimeStyles.size;\n    (0, _react.useEffect)(()=>{\n        const changed = ()=>forceUpdate((c)=>c + 1);\n        runtimeStyleChanged.add(changed);\n        if (renderedStylesSize !== runtimeStyles.size) {\n            changed();\n        }\n        return ()=>{\n            runtimeStyleChanged.delete(changed);\n        };\n    }, [\n        renderedStylesSize,\n        forceUpdate\n    ]);\n    const dplId =  false ? 0 : '';\n    return [\n        ...runtimeStyles\n    ].map((href, i)=>/*#__PURE__*/ (0, _jsxruntime.jsx)(\"link\", {\n            rel: \"stylesheet\",\n            href: \"\" + href + dplId,\n            // @ts-ignore\n            precedence: \"next\"\n        }, i));\n}\n_s(RuntimeStyles, \"Eht7Kgdrrgt5B4LSklQ7qDPo8Aw=\");\n_c4 = RuntimeStyles;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-router.js.map\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"HistoryUpdater\");\n$RefreshReg$(_c1, \"Head\");\n$RefreshReg$(_c2, \"Router\");\n$RefreshReg$(_c3, \"AppRouter\");\n$RefreshReg$(_c4, \"RuntimeStyles\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/globals/intercept-console-error.js ***!
  \*************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    originConsoleError: function() {\n        return originConsoleError;\n    },\n    patchConsoleError: function() {\n        return patchConsoleError;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst _isnextroutererror = __webpack_require__(/*! ../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _console = __webpack_require__(/*! ../../lib/console */ \"(app-pages-browser)/./node_modules/next/dist/client/lib/console.js\");\nconst originConsoleError = globalThis.console.error;\nfunction patchConsoleError() {\n    // Ensure it's only patched once\n    if (false) {}\n    window.console.error = function error() {\n        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n            args[_key] = arguments[_key];\n        }\n        let maybeError;\n        if (true) {\n            const { error: replayedError } = (0, _console.parseConsoleArgs)(args);\n            if (replayedError) {\n                maybeError = replayedError;\n            } else if ((0, _iserror.default)(args[0])) {\n                maybeError = args[0];\n            } else {\n                // See https://github.com/facebook/react/blob/d50323eb845c5fde0d720cae888bf35dedd05506/packages/react-reconciler/src/ReactFiberErrorLogger.js#L78\n                maybeError = args[1];\n            }\n        } else {}\n        if (!(0, _isnextroutererror.isNextRouterError)(maybeError)) {\n            if (true) {\n                (0, _useerrorhandler.handleConsoleError)(// but if we pass the error directly, `handleClientError` will ignore it\n                maybeError, args);\n            }\n            originConsoleError.apply(window.console, args);\n        }\n    };\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=intercept-console-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"HTTPAccessFallbackBoundary\", ({\n    enumerable: true,\n    get: function() {\n        return HTTPAccessFallbackBoundary;\n    }\n}));\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _navigationuntracked = __webpack_require__(/*! ../navigation-untracked */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation-untracked.js\");\nconst _httpaccessfallback = __webpack_require__(/*! ./http-access-fallback */ \"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/http-access-fallback.js\");\nconst _warnonce = __webpack_require__(/*! ../../../shared/lib/utils/warn-once */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/utils/warn-once.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../../../shared/lib/app-router-context.shared-runtime */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nclass HTTPAccessFallbackErrorBoundary extends _react.default.Component {\n    componentDidCatch() {\n        if ( true && this.props.missingSlots && this.props.missingSlots.size > 0 && // A missing children slot is the typical not-found case, so no need to warn\n        !this.props.missingSlots.has('children')) {\n            let warningMessage = 'No default component was found for a parallel route rendered on this page. Falling back to nearest NotFound boundary.\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/routing/parallel-routes#defaultjs\\n\\n';\n            const formattedSlots = Array.from(this.props.missingSlots).sort((a, b)=>a.localeCompare(b)).map((slot)=>\"@\" + slot).join(', ');\n            warningMessage += 'Missing slots: ' + formattedSlots;\n            (0, _warnonce.warnOnce)(warningMessage);\n        }\n    }\n    static getDerivedStateFromError(error) {\n        if ((0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\n            const httpStatus = (0, _httpaccessfallback.getAccessFallbackHTTPStatus)(error);\n            return {\n                triggeredStatus: httpStatus\n            };\n        }\n        // Re-throw if error is not for 404\n        throw error;\n    }\n    static getDerivedStateFromProps(props, state) {\n        /**\n     * Handles reset of the error boundary when a navigation happens.\n     * Ensures the error boundary does not stay enabled when navigating to a new page.\n     * Approach of setState in render is safe as it checks the previous pathname and then overrides\n     * it as outlined in https://react.dev/reference/react/useState#storing-information-from-previous-renders\n     */ if (props.pathname !== state.previousPathname && state.triggeredStatus) {\n            return {\n                triggeredStatus: undefined,\n                previousPathname: props.pathname\n            };\n        }\n        return {\n            triggeredStatus: state.triggeredStatus,\n            previousPathname: props.pathname\n        };\n    }\n    render() {\n        const { notFound, forbidden, unauthorized, children } = this.props;\n        const { triggeredStatus } = this.state;\n        const errorComponents = {\n            [_httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND]: notFound,\n            [_httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN]: forbidden,\n            [_httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED]: unauthorized\n        };\n        if (triggeredStatus) {\n            const isNotFound = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.NOT_FOUND && notFound;\n            const isForbidden = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.FORBIDDEN && forbidden;\n            const isUnauthorized = triggeredStatus === _httpaccessfallback.HTTPAccessErrorStatus.UNAUTHORIZED && unauthorized;\n            // If there's no matched boundary in this layer, keep throwing the error by rendering the children\n            if (!(isNotFound || isForbidden || isUnauthorized)) {\n                return children;\n            }\n            return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"robots\",\n                        content: \"noindex\"\n                    }),\n                     true && /*#__PURE__*/ (0, _jsxruntime.jsx)(\"meta\", {\n                        name: \"boundary-next-error\",\n                        content: (0, _httpaccessfallback.getAccessFallbackErrorTypeByStatus)(triggeredStatus)\n                    }),\n                    errorComponents[triggeredStatus]\n                ]\n            });\n        }\n        return children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            triggeredStatus: undefined,\n            previousPathname: props.pathname\n        };\n    }\n}\nfunction HTTPAccessFallbackBoundary(param) {\n    let { notFound, forbidden, unauthorized, children } = param;\n    // When we're rendering the missing params shell, this will return null. This\n    // is because we won't be rendering any not found boundaries or error\n    // boundaries for the missing params shell. When this runs on the client\n    // (where these error can occur), we will get the correct pathname.\n    const pathname = (0, _navigationuntracked.useUntrackedPathname)();\n    const missingSlots = (0, _react.useContext)(_approutercontextsharedruntime.MissingSlotContext);\n    const hasErrorFallback = !!(notFound || forbidden || unauthorized);\n    if (hasErrorFallback) {\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(HTTPAccessFallbackErrorBoundary, {\n            pathname: pathname,\n            notFound: notFound,\n            forbidden: forbidden,\n            unauthorized: unauthorized,\n            missingSlots: missingSlots,\n            children: children\n        });\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {\n        children: children\n    });\n}\n_c = HTTPAccessFallbackBoundary;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary.js.map\nvar _c;\n$RefreshReg$(_c, \"HTTPAccessFallbackBoundary\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/links.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/components/links.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    IDLE_LINK_STATUS: function() {\n        return IDLE_LINK_STATUS;\n    },\n    PENDING_LINK_STATUS: function() {\n        return PENDING_LINK_STATUS;\n    },\n    mountFormInstance: function() {\n        return mountFormInstance;\n    },\n    mountLinkInstance: function() {\n        return mountLinkInstance;\n    },\n    onLinkVisibilityChanged: function() {\n        return onLinkVisibilityChanged;\n    },\n    onNavigationIntent: function() {\n        return onNavigationIntent;\n    },\n    pingVisibleLinks: function() {\n        return pingVisibleLinks;\n    },\n    setLinkForCurrentNavigation: function() {\n        return setLinkForCurrentNavigation;\n    },\n    unmountLinkForCurrentNavigation: function() {\n        return unmountLinkForCurrentNavigation;\n    },\n    unmountPrefetchableInstance: function() {\n        return unmountPrefetchableInstance;\n    }\n});\nconst _approuterinstance = __webpack_require__(/*! ./app-router-instance */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-instance.js\");\nconst _approuter = __webpack_require__(/*! ./app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer/router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _segmentcache = __webpack_require__(/*! ./segment-cache */ \"(app-pages-browser)/./node_modules/next/dist/client/components/segment-cache.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// Tracks the most recently navigated link instance. When null, indicates\n// the current navigation was not initiated by a link click.\nlet linkForMostRecentNavigation = null;\nconst PENDING_LINK_STATUS = {\n    pending: true\n};\nconst IDLE_LINK_STATUS = {\n    pending: false\n};\nfunction setLinkForCurrentNavigation(link) {\n    (0, _react.startTransition)(()=>{\n        linkForMostRecentNavigation == null ? void 0 : linkForMostRecentNavigation.setOptimisticLinkStatus(IDLE_LINK_STATUS);\n        link == null ? void 0 : link.setOptimisticLinkStatus(PENDING_LINK_STATUS);\n        linkForMostRecentNavigation = link;\n    });\n}\nfunction unmountLinkForCurrentNavigation(link) {\n    if (linkForMostRecentNavigation === link) {\n        linkForMostRecentNavigation = null;\n    }\n}\n// Use a WeakMap to associate a Link instance with its DOM element. This is\n// used by the IntersectionObserver to track the link's visibility.\nconst prefetchable = typeof WeakMap === 'function' ? new WeakMap() : new Map();\n// A Set of the currently visible links. We re-prefetch visible links after a\n// cache invalidation, or when the current URL changes. It's a separate data\n// structure from the WeakMap above because only the visible links need to\n// be enumerated.\nconst prefetchableAndVisible = new Set();\n// A single IntersectionObserver instance shared by all <Link> components.\nconst observer = typeof IntersectionObserver === 'function' ? new IntersectionObserver(handleIntersect, {\n    rootMargin: '200px'\n}) : null;\nfunction observeVisibility(element, instance) {\n    const existingInstance = prefetchable.get(element);\n    if (existingInstance !== undefined) {\n        // This shouldn't happen because each <Link> component should have its own\n        // anchor tag instance, but it's defensive coding to avoid a memory leak in\n        // case there's a logical error somewhere else.\n        unmountPrefetchableInstance(element);\n    }\n    // Only track prefetchable links that have a valid prefetch URL\n    prefetchable.set(element, instance);\n    if (observer !== null) {\n        observer.observe(element);\n    }\n}\nfunction coercePrefetchableUrl(href) {\n    try {\n        return (0, _approuter.createPrefetchURL)(href);\n    } catch (e) {\n        // createPrefetchURL sometimes throws an error if an invalid URL is\n        // provided, though I'm not sure if it's actually necessary.\n        // TODO: Consider removing the throw from the inner function, or change it\n        // to reportError. Or maybe the error isn't even necessary for automatic\n        // prefetches, just navigations.\n        const reportErrorFn = typeof reportError === 'function' ? reportError : console.error;\n        reportErrorFn(\"Cannot prefetch '\" + href + \"' because it cannot be converted to a URL.\");\n        return null;\n    }\n}\nfunction mountLinkInstance(element, href, router, kind, prefetchEnabled, setOptimisticLinkStatus) {\n    if (prefetchEnabled) {\n        const prefetchURL = coercePrefetchableUrl(href);\n        if (prefetchURL !== null) {\n            const instance = {\n                router,\n                kind,\n                isVisible: false,\n                wasHoveredOrTouched: false,\n                prefetchTask: null,\n                cacheVersion: -1,\n                prefetchHref: prefetchURL.href,\n                setOptimisticLinkStatus\n            };\n            // We only observe the link's visibility if it's prefetchable. For\n            // example, this excludes links to external URLs.\n            observeVisibility(element, instance);\n            return instance;\n        }\n    }\n    // If the link is not prefetchable, we still create an instance so we can\n    // track its optimistic state (i.e. useLinkStatus).\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: null,\n        setOptimisticLinkStatus\n    };\n    return instance;\n}\nfunction mountFormInstance(element, href, router, kind) {\n    const prefetchURL = coercePrefetchableUrl(href);\n    if (prefetchURL === null) {\n        // This href is not prefetchable, so we don't track it.\n        // TODO: We currently observe/unobserve a form every time its href changes.\n        // For Links, this isn't a big deal because the href doesn't usually change,\n        // but for forms it's extremely common. We should optimize this.\n        return;\n    }\n    const instance = {\n        router,\n        kind,\n        isVisible: false,\n        wasHoveredOrTouched: false,\n        prefetchTask: null,\n        cacheVersion: -1,\n        prefetchHref: prefetchURL.href,\n        setOptimisticLinkStatus: null\n    };\n    observeVisibility(element, instance);\n}\nfunction unmountPrefetchableInstance(element) {\n    const instance = prefetchable.get(element);\n    if (instance !== undefined) {\n        prefetchable.delete(element);\n        prefetchableAndVisible.delete(instance);\n        const prefetchTask = instance.prefetchTask;\n        if (prefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(prefetchTask);\n        }\n    }\n    if (observer !== null) {\n        observer.unobserve(element);\n    }\n}\nfunction handleIntersect(entries) {\n    for (const entry of entries){\n        // Some extremely old browsers or polyfills don't reliably support\n        // isIntersecting so we check intersectionRatio instead. (Do we care? Not\n        // really. But whatever this is fine.)\n        const isVisible = entry.intersectionRatio > 0;\n        onLinkVisibilityChanged(entry.target, isVisible);\n    }\n}\nfunction onLinkVisibilityChanged(element, isVisible) {\n    if (true) {\n        // Prefetching on viewport is disabled in development for performance\n        // reasons, because it requires compiling the target page.\n        // TODO: Investigate re-enabling this.\n        return;\n    }\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    instance.isVisible = isVisible;\n    if (isVisible) {\n        prefetchableAndVisible.add(instance);\n    } else {\n        prefetchableAndVisible.delete(instance);\n    }\n    rescheduleLinkPrefetch(instance);\n}\nfunction onNavigationIntent(element, unstable_upgradeToDynamicPrefetch) {\n    const instance = prefetchable.get(element);\n    if (instance === undefined) {\n        return;\n    }\n    // Prefetch the link on hover/touchstart.\n    if (instance !== undefined) {\n        instance.wasHoveredOrTouched = true;\n        if (false) {}\n        rescheduleLinkPrefetch(instance);\n    }\n}\nfunction rescheduleLinkPrefetch(instance) {\n    const existingPrefetchTask = instance.prefetchTask;\n    if (!instance.isVisible) {\n        // Cancel any in-progress prefetch task. (If it already finished then this\n        // is a no-op.)\n        if (existingPrefetchTask !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(existingPrefetchTask);\n        }\n        // We don't need to reset the prefetchTask to null upon cancellation; an\n        // old task object can be rescheduled with reschedulePrefetchTask. This is a\n        // micro-optimization but also makes the code simpler (don't need to\n        // worry about whether an old task object is stale).\n        return;\n    }\n    if (true) {\n        // The old prefetch implementation does not have different priority levels.\n        // Just schedule a new prefetch task.\n        prefetchWithOldCacheImplementation(instance);\n        return;\n    }\n    // In the Segment Cache implementation, we assign a higher priority level to\n    // links that were at one point hovered or touched. Since the queue is last-\n    // in-first-out, the highest priority Link is whichever one was hovered last.\n    //\n    // We also increase the relative priority of links whenever they re-enter the\n    // viewport, as if they were being scheduled for the first time.\n    const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n    const appRouterState = (0, _approuterinstance.getCurrentAppRouterState)();\n    if (appRouterState !== null) {\n        const treeAtTimeOfPrefetch = appRouterState.tree;\n        if (existingPrefetchTask === null) {\n            // Initiate a prefetch task.\n            const nextUrl = appRouterState.nextUrl;\n            const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n            instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        } else {\n            // We already have an old task object that we can reschedule. This is\n            // effectively the same as canceling the old task and creating a new one.\n            (0, _segmentcache.reschedulePrefetchTask)(existingPrefetchTask, treeAtTimeOfPrefetch, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        }\n        // Keep track of the cache version at the time the prefetch was requested.\n        // This is used to check if the prefetch is stale.\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction pingVisibleLinks(nextUrl, tree) {\n    // For each currently visible link, cancel the existing prefetch task (if it\n    // exists) and schedule a new one. This is effectively the same as if all the\n    // visible links left and then re-entered the viewport.\n    //\n    // This is called when the Next-Url or the base tree changes, since those\n    // may affect the result of a prefetch task. It's also called after a\n    // cache invalidation.\n    const currentCacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    for (const instance of prefetchableAndVisible){\n        const task = instance.prefetchTask;\n        if (task !== null && instance.cacheVersion === currentCacheVersion && task.key.nextUrl === nextUrl && task.treeAtTimeOfPrefetch === tree) {\n            continue;\n        }\n        // Something changed. Cancel the existing prefetch task and schedule a\n        // new one.\n        if (task !== null) {\n            (0, _segmentcache.cancelPrefetchTask)(task);\n        }\n        const cacheKey = (0, _segmentcache.createCacheKey)(instance.prefetchHref, nextUrl);\n        const priority = instance.wasHoveredOrTouched ? _segmentcache.PrefetchPriority.Intent : _segmentcache.PrefetchPriority.Default;\n        instance.prefetchTask = (0, _segmentcache.schedulePrefetchTask)(cacheKey, tree, instance.kind === _routerreducertypes.PrefetchKind.FULL, priority);\n        instance.cacheVersion = (0, _segmentcache.getCurrentCacheVersion)();\n    }\n}\nfunction prefetchWithOldCacheImplementation(instance) {\n    // This is the path used when the Segment Cache is not enabled.\n    if (false) {}\n    const doPrefetch = async ()=>{\n        // note that `appRouter.prefetch()` is currently sync,\n        // so we have to wrap this call in an async function to be able to catch() errors below.\n        return instance.router.prefetch(instance.prefetchHref, {\n            kind: instance.kind\n        });\n    };\n    // Prefetch the page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=links.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/links.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js ***!
  \*******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"AppDevOverlay\", ({\n    enumerable: true,\n    get: function() {\n        return AppDevOverlay;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _appdevoverlayerrorboundary = __webpack_require__(/*! ./app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\nconst _fontstyles = __webpack_require__(/*! ../font/font-styles */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/font/font-styles.js\");\nconst _devoverlay = __webpack_require__(/*! ../ui/dev-overlay */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/ui/dev-overlay.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../../errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../../is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _constants = __webpack_require__(/*! ../../../../shared/lib/errors/constants */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/errors/constants.js\");\nfunction readSsrError() {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    const ssrErrorTemplateTag = document.querySelector('template[data-next-error-message]');\n    if (ssrErrorTemplateTag) {\n        const message = ssrErrorTemplateTag.getAttribute('data-next-error-message');\n        const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack');\n        const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest');\n        const error = Object.defineProperty(new Error(message), \"__NEXT_ERROR_CODE\", {\n            value: \"E394\",\n            enumerable: false,\n            configurable: true\n        });\n        if (digest) {\n            ;\n            error.digest = digest;\n        }\n        // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n        if ((0, _isnextroutererror.isNextRouterError)(error)) {\n            return null;\n        }\n        error.stack = stack || '';\n        return error;\n    }\n    return null;\n}\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors(param) {\n    let { onBlockingError } = param;\n    if (true) {\n        // Need to read during render. The attributes will be gone after commit.\n        const ssrError = readSsrError();\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        (0, _react.useEffect)(()=>{\n            if (ssrError !== null) {\n                // TODO(veil): Produces wrong Owner Stack\n                // TODO(veil): Mark as recoverable error\n                // TODO(veil): console.error\n                (0, _useerrorhandler.handleClientError)(ssrError);\n                // If it's missing root tags, we can't recover, make it blocking.\n                if (ssrError.digest === _constants.MISSING_ROOT_TAGS_ERROR) {\n                    onBlockingError();\n                }\n            }\n        }, [\n            ssrError,\n            onBlockingError\n        ]);\n    }\n    return null;\n}\n_c = ReplaySsrOnlyErrors;\nfunction AppDevOverlay(param) {\n    let { state, globalError, children } = param;\n    const [isErrorOverlayOpen, setIsErrorOverlayOpen] = (0, _react.useState)(false);\n    const openOverlay = (0, _react.useCallback)(()=>{\n        setIsErrorOverlayOpen(true);\n    }, []);\n    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n        children: [\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_appdevoverlayerrorboundary.AppDevOverlayErrorBoundary, {\n                globalError: globalError,\n                onError: setIsErrorOverlayOpen,\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(ReplaySsrOnlyErrors, {\n                        onBlockingError: openOverlay\n                    }),\n                    children\n                ]\n            }),\n            /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_fontstyles.FontStyles, {}),\n                    /*#__PURE__*/ (0, _jsxruntime.jsx)(_devoverlay.DevOverlay, {\n                        state: state,\n                        isErrorOverlayOpen: isErrorOverlayOpen,\n                        setIsErrorOverlayOpen: setIsErrorOverlayOpen\n                    })\n                ]\n            })\n        ]\n    });\n}\n_c1 = AppDevOverlay;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dev-overlay.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"ReplaySsrOnlyErrors\");\n$RefreshReg$(_c1, \"AppDevOverlay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js ***!
  \************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"createInitialRouterState\", ({\n    enumerable: true,\n    get: function() {\n        return createInitialRouterState;\n    }\n}));\nconst _createhreffromurl = __webpack_require__(/*! ./create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _filllazyitemstillleafwithhead = __webpack_require__(/*! ./fill-lazy-items-till-leaf-with-head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fill-lazy-items-till-leaf-with-head.js\");\nconst _computechangedpath = __webpack_require__(/*! ./compute-changed-path */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/compute-changed-path.js\");\nconst _prefetchcacheutils = __webpack_require__(/*! ./prefetch-cache-utils */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _refetchinactiveparallelsegments = __webpack_require__(/*! ./refetch-inactive-parallel-segments */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/refetch-inactive-parallel-segments.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nfunction createInitialRouterState(param) {\n    let { navigatedAt, initialFlightData, initialCanonicalUrlParts, initialParallelRoutes, location, couldBeIntercepted, postponed, prerendered } = param;\n    // When initialized on the server, the canonical URL is provided as an array of parts.\n    // This is to ensure that when the RSC payload streamed to the client, crawlers don't interpret it\n    // as a URL that should be crawled.\n    const initialCanonicalUrl = initialCanonicalUrlParts.join('/');\n    const normalizedFlightData = (0, _flightdatahelpers.getFlightDataPartsFromPath)(initialFlightData[0]);\n    const { tree: initialTree, seedData: initialSeedData, head: initialHead } = normalizedFlightData;\n    // For the SSR render, seed data should always be available (we only send back a `null` response\n    // in the case of a `loading` segment, pre-PPR.)\n    const rsc = initialSeedData == null ? void 0 : initialSeedData[1];\n    var _initialSeedData_;\n    const loading = (_initialSeedData_ = initialSeedData == null ? void 0 : initialSeedData[3]) != null ? _initialSeedData_ : null;\n    const cache = {\n        lazyData: null,\n        rsc,\n        prefetchRsc: null,\n        head: null,\n        prefetchHead: null,\n        // The cache gets seeded during the first render. `initialParallelRoutes` ensures the cache from the first render is there during the second render.\n        parallelRoutes: initialParallelRoutes,\n        loading,\n        navigatedAt\n    };\n    const canonicalUrl = // This is safe to do as canonicalUrl can't be rendered, it's only used to control the history updates in the useEffect further down in this file.\n    location ? (0, _createhreffromurl.createHrefFromUrl)(location) : initialCanonicalUrl;\n    (0, _refetchinactiveparallelsegments.addRefreshMarkerToActiveParallelSegments)(initialTree, canonicalUrl);\n    const prefetchCache = new Map();\n    // When the cache hasn't been seeded yet we fill the cache with the head.\n    if (initialParallelRoutes === null || initialParallelRoutes.size === 0) {\n        (0, _filllazyitemstillleafwithhead.fillLazyItemsTillLeafWithHead)(navigatedAt, cache, undefined, initialTree, initialSeedData, initialHead, undefined);\n    }\n    var _ref;\n    const initialState = {\n        tree: initialTree,\n        cache,\n        prefetchCache,\n        pushRef: {\n            pendingPush: false,\n            mpaNavigation: false,\n            // First render needs to preserve the previous window.history.state\n            // to avoid it being overwritten on navigation back/forward with MPA Navigation.\n            preserveCustomHistoryState: true\n        },\n        focusAndScrollRef: {\n            apply: false,\n            onlyHashChange: false,\n            hashFragment: null,\n            segmentPaths: []\n        },\n        canonicalUrl,\n        nextUrl: (_ref = (0, _computechangedpath.extractPathFromFlightRouterState)(initialTree) || (location == null ? void 0 : location.pathname)) != null ? _ref : null\n    };\n    if (false) {}\n    return initialState;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=create-initial-router-state.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvY3JlYXRlLWluaXRpYWwtcm91dGVyLXN0YXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NERBc0JnQkE7OztlQUFBQTs7OytDQW5Ca0I7MkRBQ1k7Z0RBQ0c7Z0RBQ0Y7Z0RBQ087NkRBQ0c7K0NBQ2Q7QUFhcEMsU0FBU0EseUJBQXlCLEtBU1Y7SUFUVSxNQUN2Q0MsV0FBVyxFQUNYQyxpQkFBaUIsRUFDakJDLHdCQUF3QixFQUN4QkMscUJBQXFCLEVBQ3JCQyxRQUFRLEVBQ1JDLGtCQUFrQixFQUNsQkMsU0FBUyxFQUNUQyxXQUFXLEVBQ2tCLEdBVFU7SUFVdkMsc0ZBQXNGO0lBQ3RGLGtHQUFrRztJQUNsRyxtQ0FBbUM7SUFDbkMsTUFBTUMsc0JBQXNCTix5QkFBeUJPLElBQUksQ0FBQztJQUMxRCxNQUFNQyx1QkFBdUJDLENBQUFBLEdBQUFBLG1CQUFBQSwwQkFBMEIsRUFBQ1YsaUJBQWlCLENBQUMsRUFBRTtJQUM1RSxNQUFNLEVBQ0pXLE1BQU1DLFdBQVcsRUFDakJDLFVBQVVDLGVBQWUsRUFDekJDLE1BQU1DLFdBQVcsRUFDbEIsR0FBR1A7SUFDSixnR0FBZ0c7SUFDaEcsZ0RBQWdEO0lBQ2hELE1BQU1RLE1BQU1ILG1CQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxlQUFpQixDQUFDLEVBQUU7UUFDaEJBO0lBQWhCLE1BQU1JLFVBQVVKLHFCQUFBQSxtQkFBQUEsT0FBQUEsS0FBQUEsSUFBQUEsZUFBaUIsQ0FBQyxPQUFFLE9BQXBCQSxvQkFBd0I7SUFFeEMsTUFBTUssUUFBbUI7UUFDdkJDLFVBQVU7UUFDVkg7UUFDQUksYUFBYTtRQUNiTixNQUFNO1FBQ05PLGNBQWM7UUFDZCxvSkFBb0o7UUFDcEpDLGdCQUFnQnJCO1FBQ2hCZ0I7UUFDQW5CO0lBQ0Y7SUFFQSxNQUFNeUIsZUFDSiw2RUFBNkUscUVBQ3FFO0lBQ2xKckIsV0FFSXNCLENBQUFBLEdBQUFBLG1CQUFBQSxpQkFBQUEsRUFBa0J0QixZQUNsQkk7SUFFTm1CLENBQUFBLEdBQUFBLGlDQUFBQSx3Q0FBQUEsRUFBeUNkLGFBQWFZO0lBRXRELE1BQU1HLGdCQUFnQixJQUFJQztJQUUxQix5RUFBeUU7SUFDekUsSUFBSTFCLDBCQUEwQixRQUFRQSxzQkFBc0IyQixJQUFJLEtBQUssR0FBRztRQUN0RUMsQ0FBQUEsR0FBQUEsK0JBQUFBLDZCQUE2QixFQUMzQi9CLGFBQ0FvQixPQUNBWSxXQUNBbkIsYUFDQUUsaUJBQ0FFLGFBQ0FlO0lBRUo7UUFxQkk7SUFuQkosTUFBTUUsZUFBZTtRQUNuQnRCLE1BQU1DO1FBQ05PO1FBQ0FRO1FBQ0FPLFNBQVM7WUFDUEMsYUFBYTtZQUNiQyxlQUFlO1lBQ2YsbUVBQW1FO1lBQ25FLGdGQUFnRjtZQUNoRkMsNEJBQTRCO1FBQzlCO1FBQ0FDLG1CQUFtQjtZQUNqQkMsT0FBTztZQUNQQyxnQkFBZ0I7WUFDaEJDLGNBQWM7WUFDZEMsY0FBYyxFQUFFO1FBQ2xCO1FBQ0FsQjtRQUNBbUIsU0FFRSxDQUFDWCxPQUFBQSxDQUFBQSxHQUFBQSxvQkFBQUEsZ0NBQUFBLEVBQWlDcEIsZ0JBQWdCVCxhQUFBQSxPQUFBQSxLQUFBQSxJQUFBQSxTQUFVeUMsUUFBQUEsQ0FBUSxZQUFuRVosT0FDRDtJQUNKO0lBRUEsSUFBSWEsS0FBa0QxQyxFQUFFLEVBaUN2RDtJQUVELE9BQU84QjtBQUNUIiwic291cmNlcyI6WyIvc3JjL2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL2NyZWF0ZS1pbml0aWFsLXJvdXRlci1zdGF0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IENhY2hlTm9kZSB9IGZyb20gJy4uLy4uLy4uL3NoYXJlZC9saWIvYXBwLXJvdXRlci1jb250ZXh0LnNoYXJlZC1ydW50aW1lJ1xuaW1wb3J0IHR5cGUgeyBGbGlnaHREYXRhUGF0aCB9IGZyb20gJy4uLy4uLy4uL3NlcnZlci9hcHAtcmVuZGVyL3R5cGVzJ1xuXG5pbXBvcnQgeyBjcmVhdGVIcmVmRnJvbVVybCB9IGZyb20gJy4vY3JlYXRlLWhyZWYtZnJvbS11cmwnXG5pbXBvcnQgeyBmaWxsTGF6eUl0ZW1zVGlsbExlYWZXaXRoSGVhZCB9IGZyb20gJy4vZmlsbC1sYXp5LWl0ZW1zLXRpbGwtbGVhZi13aXRoLWhlYWQnXG5pbXBvcnQgeyBleHRyYWN0UGF0aEZyb21GbGlnaHRSb3V0ZXJTdGF0ZSB9IGZyb20gJy4vY29tcHV0ZS1jaGFuZ2VkLXBhdGgnXG5pbXBvcnQgeyBjcmVhdGVTZWVkZWRQcmVmZXRjaENhY2hlRW50cnkgfSBmcm9tICcuL3ByZWZldGNoLWNhY2hlLXV0aWxzJ1xuaW1wb3J0IHsgUHJlZmV0Y2hLaW5kLCB0eXBlIFByZWZldGNoQ2FjaGVFbnRyeSB9IGZyb20gJy4vcm91dGVyLXJlZHVjZXItdHlwZXMnXG5pbXBvcnQgeyBhZGRSZWZyZXNoTWFya2VyVG9BY3RpdmVQYXJhbGxlbFNlZ21lbnRzIH0gZnJvbSAnLi9yZWZldGNoLWluYWN0aXZlLXBhcmFsbGVsLXNlZ21lbnRzJ1xuaW1wb3J0IHsgZ2V0RmxpZ2h0RGF0YVBhcnRzRnJvbVBhdGggfSBmcm9tICcuLi8uLi9mbGlnaHQtZGF0YS1oZWxwZXJzJ1xuXG5leHBvcnQgaW50ZXJmYWNlIEluaXRpYWxSb3V0ZXJTdGF0ZVBhcmFtZXRlcnMge1xuICBuYXZpZ2F0ZWRBdDogbnVtYmVyXG4gIGluaXRpYWxDYW5vbmljYWxVcmxQYXJ0czogc3RyaW5nW11cbiAgaW5pdGlhbFBhcmFsbGVsUm91dGVzOiBDYWNoZU5vZGVbJ3BhcmFsbGVsUm91dGVzJ11cbiAgaW5pdGlhbEZsaWdodERhdGE6IEZsaWdodERhdGFQYXRoW11cbiAgbG9jYXRpb246IExvY2F0aW9uIHwgbnVsbFxuICBjb3VsZEJlSW50ZXJjZXB0ZWQ6IGJvb2xlYW5cbiAgcG9zdHBvbmVkOiBib29sZWFuXG4gIHByZXJlbmRlcmVkOiBib29sZWFuXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjcmVhdGVJbml0aWFsUm91dGVyU3RhdGUoe1xuICBuYXZpZ2F0ZWRBdCxcbiAgaW5pdGlhbEZsaWdodERhdGEsXG4gIGluaXRpYWxDYW5vbmljYWxVcmxQYXJ0cyxcbiAgaW5pdGlhbFBhcmFsbGVsUm91dGVzLFxuICBsb2NhdGlvbixcbiAgY291bGRCZUludGVyY2VwdGVkLFxuICBwb3N0cG9uZWQsXG4gIHByZXJlbmRlcmVkLFxufTogSW5pdGlhbFJvdXRlclN0YXRlUGFyYW1ldGVycykge1xuICAvLyBXaGVuIGluaXRpYWxpemVkIG9uIHRoZSBzZXJ2ZXIsIHRoZSBjYW5vbmljYWwgVVJMIGlzIHByb3ZpZGVkIGFzIGFuIGFycmF5IG9mIHBhcnRzLlxuICAvLyBUaGlzIGlzIHRvIGVuc3VyZSB0aGF0IHdoZW4gdGhlIFJTQyBwYXlsb2FkIHN0cmVhbWVkIHRvIHRoZSBjbGllbnQsIGNyYXdsZXJzIGRvbid0IGludGVycHJldCBpdFxuICAvLyBhcyBhIFVSTCB0aGF0IHNob3VsZCBiZSBjcmF3bGVkLlxuICBjb25zdCBpbml0aWFsQ2Fub25pY2FsVXJsID0gaW5pdGlhbENhbm9uaWNhbFVybFBhcnRzLmpvaW4oJy8nKVxuICBjb25zdCBub3JtYWxpemVkRmxpZ2h0RGF0YSA9IGdldEZsaWdodERhdGFQYXJ0c0Zyb21QYXRoKGluaXRpYWxGbGlnaHREYXRhWzBdKVxuICBjb25zdCB7XG4gICAgdHJlZTogaW5pdGlhbFRyZWUsXG4gICAgc2VlZERhdGE6IGluaXRpYWxTZWVkRGF0YSxcbiAgICBoZWFkOiBpbml0aWFsSGVhZCxcbiAgfSA9IG5vcm1hbGl6ZWRGbGlnaHREYXRhXG4gIC8vIEZvciB0aGUgU1NSIHJlbmRlciwgc2VlZCBkYXRhIHNob3VsZCBhbHdheXMgYmUgYXZhaWxhYmxlICh3ZSBvbmx5IHNlbmQgYmFjayBhIGBudWxsYCByZXNwb25zZVxuICAvLyBpbiB0aGUgY2FzZSBvZiBhIGBsb2FkaW5nYCBzZWdtZW50LCBwcmUtUFBSLilcbiAgY29uc3QgcnNjID0gaW5pdGlhbFNlZWREYXRhPy5bMV1cbiAgY29uc3QgbG9hZGluZyA9IGluaXRpYWxTZWVkRGF0YT8uWzNdID8/IG51bGxcblxuICBjb25zdCBjYWNoZTogQ2FjaGVOb2RlID0ge1xuICAgIGxhenlEYXRhOiBudWxsLFxuICAgIHJzYyxcbiAgICBwcmVmZXRjaFJzYzogbnVsbCxcbiAgICBoZWFkOiBudWxsLFxuICAgIHByZWZldGNoSGVhZDogbnVsbCxcbiAgICAvLyBUaGUgY2FjaGUgZ2V0cyBzZWVkZWQgZHVyaW5nIHRoZSBmaXJzdCByZW5kZXIuIGBpbml0aWFsUGFyYWxsZWxSb3V0ZXNgIGVuc3VyZXMgdGhlIGNhY2hlIGZyb20gdGhlIGZpcnN0IHJlbmRlciBpcyB0aGVyZSBkdXJpbmcgdGhlIHNlY29uZCByZW5kZXIuXG4gICAgcGFyYWxsZWxSb3V0ZXM6IGluaXRpYWxQYXJhbGxlbFJvdXRlcyxcbiAgICBsb2FkaW5nLFxuICAgIG5hdmlnYXRlZEF0LFxuICB9XG5cbiAgY29uc3QgY2Fub25pY2FsVXJsID1cbiAgICAvLyBsb2NhdGlvbi5ocmVmIGlzIHJlYWQgYXMgdGhlIGluaXRpYWwgdmFsdWUgZm9yIGNhbm9uaWNhbFVybCBpbiB0aGUgYnJvd3NlclxuICAgIC8vIFRoaXMgaXMgc2FmZSB0byBkbyBhcyBjYW5vbmljYWxVcmwgY2FuJ3QgYmUgcmVuZGVyZWQsIGl0J3Mgb25seSB1c2VkIHRvIGNvbnRyb2wgdGhlIGhpc3RvcnkgdXBkYXRlcyBpbiB0aGUgdXNlRWZmZWN0IGZ1cnRoZXIgZG93biBpbiB0aGlzIGZpbGUuXG4gICAgbG9jYXRpb25cbiAgICAgID8gLy8gd2luZG93LmxvY2F0aW9uIGRvZXMgbm90IGhhdmUgdGhlIHNhbWUgdHlwZSBhcyBVUkwgYnV0IGhhcyBhbGwgdGhlIGZpZWxkcyBjcmVhdGVIcmVmRnJvbVVybCBuZWVkcy5cbiAgICAgICAgY3JlYXRlSHJlZkZyb21VcmwobG9jYXRpb24pXG4gICAgICA6IGluaXRpYWxDYW5vbmljYWxVcmxcblxuICBhZGRSZWZyZXNoTWFya2VyVG9BY3RpdmVQYXJhbGxlbFNlZ21lbnRzKGluaXRpYWxUcmVlLCBjYW5vbmljYWxVcmwpXG5cbiAgY29uc3QgcHJlZmV0Y2hDYWNoZSA9IG5ldyBNYXA8c3RyaW5nLCBQcmVmZXRjaENhY2hlRW50cnk+KClcblxuICAvLyBXaGVuIHRoZSBjYWNoZSBoYXNuJ3QgYmVlbiBzZWVkZWQgeWV0IHdlIGZpbGwgdGhlIGNhY2hlIHdpdGggdGhlIGhlYWQuXG4gIGlmIChpbml0aWFsUGFyYWxsZWxSb3V0ZXMgPT09IG51bGwgfHwgaW5pdGlhbFBhcmFsbGVsUm91dGVzLnNpemUgPT09IDApIHtcbiAgICBmaWxsTGF6eUl0ZW1zVGlsbExlYWZXaXRoSGVhZChcbiAgICAgIG5hdmlnYXRlZEF0LFxuICAgICAgY2FjaGUsXG4gICAgICB1bmRlZmluZWQsXG4gICAgICBpbml0aWFsVHJlZSxcbiAgICAgIGluaXRpYWxTZWVkRGF0YSxcbiAgICAgIGluaXRpYWxIZWFkLFxuICAgICAgdW5kZWZpbmVkXG4gICAgKVxuICB9XG5cbiAgY29uc3QgaW5pdGlhbFN0YXRlID0ge1xuICAgIHRyZWU6IGluaXRpYWxUcmVlLFxuICAgIGNhY2hlLFxuICAgIHByZWZldGNoQ2FjaGUsXG4gICAgcHVzaFJlZjoge1xuICAgICAgcGVuZGluZ1B1c2g6IGZhbHNlLFxuICAgICAgbXBhTmF2aWdhdGlvbjogZmFsc2UsXG4gICAgICAvLyBGaXJzdCByZW5kZXIgbmVlZHMgdG8gcHJlc2VydmUgdGhlIHByZXZpb3VzIHdpbmRvdy5oaXN0b3J5LnN0YXRlXG4gICAgICAvLyB0byBhdm9pZCBpdCBiZWluZyBvdmVyd3JpdHRlbiBvbiBuYXZpZ2F0aW9uIGJhY2svZm9yd2FyZCB3aXRoIE1QQSBOYXZpZ2F0aW9uLlxuICAgICAgcHJlc2VydmVDdXN0b21IaXN0b3J5U3RhdGU6IHRydWUsXG4gICAgfSxcbiAgICBmb2N1c0FuZFNjcm9sbFJlZjoge1xuICAgICAgYXBwbHk6IGZhbHNlLFxuICAgICAgb25seUhhc2hDaGFuZ2U6IGZhbHNlLFxuICAgICAgaGFzaEZyYWdtZW50OiBudWxsLFxuICAgICAgc2VnbWVudFBhdGhzOiBbXSxcbiAgICB9LFxuICAgIGNhbm9uaWNhbFVybCxcbiAgICBuZXh0VXJsOlxuICAgICAgLy8gdGhlIHx8IG9wZXJhdG9yIGlzIGludGVudGlvbmFsLCB0aGUgcGF0aG5hbWUgY2FuIGJlIGFuIGVtcHR5IHN0cmluZ1xuICAgICAgKGV4dHJhY3RQYXRoRnJvbUZsaWdodFJvdXRlclN0YXRlKGluaXRpYWxUcmVlKSB8fCBsb2NhdGlvbj8ucGF0aG5hbWUpID8/XG4gICAgICBudWxsLFxuICB9XG5cbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAnZGV2ZWxvcG1lbnQnICYmIGxvY2F0aW9uKSB7XG4gICAgLy8gU2VlZCB0aGUgcHJlZmV0Y2ggY2FjaGUgd2l0aCB0aGlzIHBhZ2UncyBkYXRhLlxuICAgIC8vIFRoaXMgaXMgdG8gcHJldmVudCBuZWVkbGVzc2x5IHJlLXByZWZldGNoaW5nIGEgcGFnZSB0aGF0IGlzIGFscmVhZHkgcmV1c2FibGUsXG4gICAgLy8gYW5kIHdpbGwgYXZvaWQgdHJpZ2dlcmluZyBhIGxvYWRpbmcgc3RhdGUvZGF0YSBmZXRjaCBzdGFsbCB3aGVuIG5hdmlnYXRpbmcgYmFjayB0byB0aGUgcGFnZS5cbiAgICAvLyBXZSBkb24ndCBjdXJyZW50bHkgZG8gdGhpcyBpbiBkZXZlbG9wbWVudCBiZWNhdXNlIGxpbmtzIGFyZW4ndCBwcmVmZXRjaGVkIGluIGRldmVsb3BtZW50XG4gICAgLy8gc28gaGF2aW5nIGEgbWlzbWF0Y2ggYmV0d2VlbiBwcmVmZXRjaC9ubyBwcmVmZXRjaCBwcm92aWRlcyBpbmNvbnNpc3RlbnQgYmVoYXZpb3IgYmFzZWQgb24gd2hpY2ggcGFnZVxuICAgIC8vIHdhcyBsb2FkZWQgZmlyc3QuXG4gICAgY29uc3QgdXJsID0gbmV3IFVSTChcbiAgICAgIGAke2xvY2F0aW9uLnBhdGhuYW1lfSR7bG9jYXRpb24uc2VhcmNofWAsXG4gICAgICBsb2NhdGlvbi5vcmlnaW5cbiAgICApXG5cbiAgICBjcmVhdGVTZWVkZWRQcmVmZXRjaENhY2hlRW50cnkoe1xuICAgICAgdXJsLFxuICAgICAgZGF0YToge1xuICAgICAgICBmbGlnaHREYXRhOiBbbm9ybWFsaXplZEZsaWdodERhdGFdLFxuICAgICAgICBjYW5vbmljYWxVcmw6IHVuZGVmaW5lZCxcbiAgICAgICAgY291bGRCZUludGVyY2VwdGVkOiAhIWNvdWxkQmVJbnRlcmNlcHRlZCxcbiAgICAgICAgcHJlcmVuZGVyZWQsXG4gICAgICAgIHBvc3Rwb25lZCxcbiAgICAgICAgLy8gVE9ETzogVGhlIGluaXRpYWwgUlNDIHBheWxvYWQgaW5jbHVkZXMgYm90aCBzdGF0aWMgYW5kIGR5bmFtaWMgZGF0YVxuICAgICAgICAvLyBpbiB0aGUgc2FtZSByZXNwb25zZSwgZXZlbiBpZiBQUFIgaXMgZW5hYmxlZC4gU28gaWYgdGhlcmUncyBhbnlcbiAgICAgICAgLy8gZHluYW1pYyBkYXRhIGF0IGFsbCwgd2UgY2FuJ3Qgc2V0IGEgc3RhbGUgdGltZS4gSW4gdGhlIGZ1dHVyZSB3ZSBtYXlcbiAgICAgICAgLy8gYWRkIGEgd2F5IHRvIHNwbGl0IGEgc2luZ2xlIEZsaWdodCBzdHJlYW0gaW50byBzdGF0aWMgYW5kIGR5bmFtaWNcbiAgICAgICAgLy8gcGFydHMuIEJ1dCBpbiB0aGUgbWVhbnRpbWUgd2Ugc2hvdWxkIGF0IGxlYXN0IG1ha2UgdGhpcyB3b3JrIGZvclxuICAgICAgICAvLyBmdWxseSBzdGF0aWMgcGFnZXMuXG4gICAgICAgIHN0YWxlVGltZTogLTEsXG4gICAgICB9LFxuICAgICAgdHJlZTogaW5pdGlhbFN0YXRlLnRyZWUsXG4gICAgICBwcmVmZXRjaENhY2hlOiBpbml0aWFsU3RhdGUucHJlZmV0Y2hDYWNoZSxcbiAgICAgIG5leHRVcmw6IGluaXRpYWxTdGF0ZS5uZXh0VXJsLFxuICAgICAga2luZDogcHJlcmVuZGVyZWQgPyBQcmVmZXRjaEtpbmQuRlVMTCA6IFByZWZldGNoS2luZC5BVVRPLFxuICAgIH0pXG4gIH1cblxuICByZXR1cm4gaW5pdGlhbFN0YXRlXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlSW5pdGlhbFJvdXRlclN0YXRlIiwibmF2aWdhdGVkQXQiLCJpbml0aWFsRmxpZ2h0RGF0YSIsImluaXRpYWxDYW5vbmljYWxVcmxQYXJ0cyIsImluaXRpYWxQYXJhbGxlbFJvdXRlcyIsImxvY2F0aW9uIiwiY291bGRCZUludGVyY2VwdGVkIiwicG9zdHBvbmVkIiwicHJlcmVuZGVyZWQiLCJpbml0aWFsQ2Fub25pY2FsVXJsIiwiam9pbiIsIm5vcm1hbGl6ZWRGbGlnaHREYXRhIiwiZ2V0RmxpZ2h0RGF0YVBhcnRzRnJvbVBhdGgiLCJ0cmVlIiwiaW5pdGlhbFRyZWUiLCJzZWVkRGF0YSIsImluaXRpYWxTZWVkRGF0YSIsImhlYWQiLCJpbml0aWFsSGVhZCIsInJzYyIsImxvYWRpbmciLCJjYWNoZSIsImxhenlEYXRhIiwicHJlZmV0Y2hSc2MiLCJwcmVmZXRjaEhlYWQiLCJwYXJhbGxlbFJvdXRlcyIsImNhbm9uaWNhbFVybCIsImNyZWF0ZUhyZWZGcm9tVXJsIiwiYWRkUmVmcmVzaE1hcmtlclRvQWN0aXZlUGFyYWxsZWxTZWdtZW50cyIsInByZWZldGNoQ2FjaGUiLCJNYXAiLCJzaXplIiwiZmlsbExhenlJdGVtc1RpbGxMZWFmV2l0aEhlYWQiLCJ1bmRlZmluZWQiLCJleHRyYWN0UGF0aEZyb21GbGlnaHRSb3V0ZXJTdGF0ZSIsImluaXRpYWxTdGF0ZSIsInB1c2hSZWYiLCJwZW5kaW5nUHVzaCIsIm1wYU5hdmlnYXRpb24iLCJwcmVzZXJ2ZUN1c3RvbUhpc3RvcnlTdGF0ZSIsImZvY3VzQW5kU2Nyb2xsUmVmIiwiYXBwbHkiLCJvbmx5SGFzaENoYW5nZSIsImhhc2hGcmFnbWVudCIsInNlZ21lbnRQYXRocyIsIm5leHRVcmwiLCJwYXRobmFtZSIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsInVybCIsIlVSTCIsInNlYXJjaCIsIm9yaWdpbiIsImNyZWF0ZVNlZWRlZFByZWZldGNoQ2FjaGVFbnRyeSIsImRhdGEiLCJmbGlnaHREYXRhIiwic3RhbGVUaW1lIiwia2luZCIsIlByZWZldGNoS2luZCIsIkZVTEwiLCJBVVRPIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-initial-router-state.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    createFetch: function() {\n        return createFetch;\n    },\n    createFromNextReadableStream: function() {\n        return createFromNextReadableStream;\n    },\n    fetchServerResponse: function() {\n        return fetchServerResponse;\n    },\n    urlToUrlWithoutFlightMarker: function() {\n        return urlToUrlWithoutFlightMarker;\n    }\n});\nconst _approuterheaders = __webpack_require__(/*! ../app-router-headers */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router-headers.js\");\nconst _appcallserver = __webpack_require__(/*! ../../app-call-server */ \"(app-pages-browser)/./node_modules/next/dist/client/app-call-server.js\");\nconst _appfindsourcemapurl = __webpack_require__(/*! ../../app-find-source-map-url */ \"(app-pages-browser)/./node_modules/next/dist/client/app-find-source-map-url.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _flightdatahelpers = __webpack_require__(/*! ../../flight-data-helpers */ \"(app-pages-browser)/./node_modules/next/dist/client/flight-data-helpers.js\");\nconst _appbuildid = __webpack_require__(/*! ../../app-build-id */ \"(app-pages-browser)/./node_modules/next/dist/client/app-build-id.js\");\nconst _setcachebustingsearchparam = __webpack_require__(/*! ./set-cache-busting-search-param */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/set-cache-busting-search-param.js\");\n// @ts-ignore\n// eslint-disable-next-line import/no-extraneous-dependencies\n// import { createFromReadableStream } from 'react-server-dom-webpack/client'\nconst { createFromReadableStream } =  false ? 0 : __webpack_require__(/*! react-server-dom-webpack/client */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-server-dom-webpack/client.js\");\nfunction urlToUrlWithoutFlightMarker(url) {\n    const urlWithoutFlightParameters = new URL(url, location.origin);\n    urlWithoutFlightParameters.searchParams.delete(_approuterheaders.NEXT_RSC_UNION_QUERY);\n    if (false) {}\n    return urlWithoutFlightParameters;\n}\nfunction doMpaNavigation(url) {\n    return {\n        flightData: urlToUrlWithoutFlightMarker(url).toString(),\n        canonicalUrl: undefined,\n        couldBeIntercepted: false,\n        prerendered: false,\n        postponed: false,\n        staleTime: -1\n    };\n}\nlet abortController = new AbortController();\nif (true) {\n    // Abort any in-flight requests when the page is unloaded, e.g. due to\n    // reloading the page or performing hard navigations. This allows us to ignore\n    // what would otherwise be a thrown TypeError when the browser cancels the\n    // requests.\n    window.addEventListener('pagehide', ()=>{\n        abortController.abort();\n    });\n    // Use a fresh AbortController instance on pageshow, e.g. when navigating back\n    // and the JavaScript execution context is restored by the browser.\n    window.addEventListener('pageshow', ()=>{\n        abortController = new AbortController();\n    });\n}\nasync function fetchServerResponse(url, options) {\n    const { flightRouterState, nextUrl, prefetchKind } = options;\n    const headers = {\n        // Enable flight response\n        [_approuterheaders.RSC_HEADER]: '1',\n        // Provide the current router state\n        [_approuterheaders.NEXT_ROUTER_STATE_TREE_HEADER]: encodeURIComponent(JSON.stringify(flightRouterState))\n    };\n    /**\n   * Three cases:\n   * - `prefetchKind` is `undefined`, it means it's a normal navigation, so we want to prefetch the page data fully\n   * - `prefetchKind` is `full` - we want to prefetch the whole page so same as above\n   * - `prefetchKind` is `auto` - if the page is dynamic, prefetch the page data partially, if static prefetch the page data fully\n   */ if (prefetchKind === _routerreducertypes.PrefetchKind.AUTO) {\n        headers[_approuterheaders.NEXT_ROUTER_PREFETCH_HEADER] = '1';\n    }\n    if ( true && options.isHmrRefresh) {\n        headers[_approuterheaders.NEXT_HMR_REFRESH_HEADER] = '1';\n    }\n    if (nextUrl) {\n        headers[_approuterheaders.NEXT_URL] = nextUrl;\n    }\n    try {\n        var _res_headers_get;\n        // When creating a \"temporary\" prefetch (the \"on-demand\" prefetch that gets created on navigation, if one doesn't exist)\n        // we send the request with a \"high\" priority as it's in response to a user interaction that could be blocking a transition.\n        // Otherwise, all other prefetches are sent with a \"low\" priority.\n        // We use \"auto\" for in all other cases to match the existing default, as this function is shared outside of prefetching.\n        const fetchPriority = prefetchKind ? prefetchKind === _routerreducertypes.PrefetchKind.TEMPORARY ? 'high' : 'low' : 'auto';\n        if (false) {}\n        const res = await createFetch(url, headers, fetchPriority, abortController.signal);\n        const responseUrl = urlToUrlWithoutFlightMarker(res.url);\n        const canonicalUrl = res.redirected ? responseUrl : undefined;\n        const contentType = res.headers.get('content-type') || '';\n        const interception = !!((_res_headers_get = res.headers.get('vary')) == null ? void 0 : _res_headers_get.includes(_approuterheaders.NEXT_URL));\n        const postponed = !!res.headers.get(_approuterheaders.NEXT_DID_POSTPONE_HEADER);\n        const staleTimeHeaderSeconds = res.headers.get(_approuterheaders.NEXT_ROUTER_STALE_TIME_HEADER);\n        const staleTime = staleTimeHeaderSeconds !== null ? parseInt(staleTimeHeaderSeconds, 10) * 1000 : -1;\n        let isFlightResponse = contentType.startsWith(_approuterheaders.RSC_CONTENT_TYPE_HEADER);\n        if (false) {}\n        // If fetch returns something different than flight response handle it like a mpa navigation\n        // If the fetch was not 200, we also handle it like a mpa navigation\n        if (!isFlightResponse || !res.ok || !res.body) {\n            // in case the original URL came with a hash, preserve it before redirecting to the new URL\n            if (url.hash) {\n                responseUrl.hash = url.hash;\n            }\n            return doMpaNavigation(responseUrl.toString());\n        }\n        // We may navigate to a page that requires a different Webpack runtime.\n        // In prod, every page will have the same Webpack runtime.\n        // In dev, the Webpack runtime is minimal for each page.\n        // We need to ensure the Webpack runtime is updated before executing client-side JS of the new page.\n        if (true) {\n            await (__webpack_require__(/*! ../react-dev-overlay/app/hot-reloader-client */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js\").waitForWebpackRuntimeHotUpdate)();\n        }\n        // Handle the `fetch` readable stream that can be unwrapped by `React.use`.\n        const flightStream = postponed ? createUnclosingPrefetchStream(res.body) : res.body;\n        const response = await createFromNextReadableStream(flightStream);\n        if ((0, _appbuildid.getAppBuildId)() !== response.b) {\n            return doMpaNavigation(res.url);\n        }\n        return {\n            flightData: (0, _flightdatahelpers.normalizeFlightData)(response.f),\n            canonicalUrl: canonicalUrl,\n            couldBeIntercepted: interception,\n            prerendered: response.S,\n            postponed,\n            staleTime\n        };\n    } catch (err) {\n        if (!abortController.signal.aborted) {\n            console.error(\"Failed to fetch RSC payload for \" + url + \". Falling back to browser navigation.\", err);\n        }\n        // If fetch fails handle it like a mpa navigation\n        // TODO-APP: Add a test for the case where a CORS request fails, e.g. external url redirect coming from the response.\n        // See https://github.com/vercel/next.js/issues/43605#issuecomment-1451617521 for a reproduction.\n        return {\n            flightData: url.toString(),\n            canonicalUrl: undefined,\n            couldBeIntercepted: false,\n            prerendered: false,\n            postponed: false,\n            staleTime: -1\n        };\n    }\n}\nfunction createFetch(url, headers, fetchPriority, signal) {\n    const fetchUrl = new URL(url);\n    // TODO: In output: \"export\" mode, the headers do nothing. Omit them (and the\n    // cache busting search param) from the request so they're\n    // maximally cacheable.\n    (0, _setcachebustingsearchparam.setCacheBustingSearchParam)(fetchUrl, headers);\n    if (false) {}\n    if (false) {}\n    return fetch(fetchUrl, {\n        // Backwards compat for older browsers. `same-origin` is the default in modern browsers.\n        credentials: 'same-origin',\n        headers,\n        priority: fetchPriority || undefined,\n        signal\n    });\n}\nfunction createFromNextReadableStream(flightStream) {\n    return createFromReadableStream(flightStream, {\n        callServer: _appcallserver.callServer,\n        findSourceMapURL: _appfindsourcemapurl.findSourceMapURL\n    });\n}\nfunction createUnclosingPrefetchStream(originalFlightStream) {\n    // When PPR is enabled, prefetch streams may contain references that never\n    // resolve, because that's how we encode dynamic data access. In the decoded\n    // object returned by the Flight client, these are reified into hanging\n    // promises that suspend during render, which is effectively what we want.\n    // The UI resolves when it switches to the dynamic data stream\n    // (via useDeferredValue(dynamic, static)).\n    //\n    // However, the Flight implementation currently errors if the server closes\n    // the response before all the references are resolved. As a cheat to work\n    // around this, we wrap the original stream in a new stream that never closes,\n    // and therefore doesn't error.\n    const reader = originalFlightStream.getReader();\n    return new ReadableStream({\n        async pull (controller) {\n            while(true){\n                const { done, value } = await reader.read();\n                if (!done) {\n                    // Pass to the target stream and keep consuming the Flight response\n                    // from the server.\n                    controller.enqueue(value);\n                    continue;\n                }\n                // The server stream has closed. Exit, but intentionally do not close\n                // the target stream.\n                return;\n            }\n        }\n    });\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=fetch-server-response.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js ***!
  \*****************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    DYNAMIC_STALETIME_MS: function() {\n        return DYNAMIC_STALETIME_MS;\n    },\n    STATIC_STALETIME_MS: function() {\n        return STATIC_STALETIME_MS;\n    },\n    createSeededPrefetchCacheEntry: function() {\n        return createSeededPrefetchCacheEntry;\n    },\n    getOrCreatePrefetchCacheEntry: function() {\n        return getOrCreatePrefetchCacheEntry;\n    },\n    prunePrefetchCache: function() {\n        return prunePrefetchCache;\n    }\n});\nconst _fetchserverresponse = __webpack_require__(/*! ./fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./router-reducer-types */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst _prefetchreducer = __webpack_require__(/*! ./reducers/prefetch-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/prefetch-reducer.js\");\nconst INTERCEPTION_CACHE_KEY_MARKER = '%';\n/**\n * Creates a cache key for the router prefetch cache\n *\n * @param url - The URL being navigated to\n * @param nextUrl - an internal URL, primarily used for handling rewrites. Defaults to '/'.\n * @return The generated prefetch cache key.\n */ function createPrefetchCacheKeyImpl(url, includeSearchParams, prefix) {\n    // Initially we only use the pathname as the cache key. We don't want to include\n    // search params so that multiple URLs with the same search parameter can re-use\n    // loading states.\n    let pathnameFromUrl = url.pathname;\n    // RSC responses can differ based on search params, specifically in the case where we aren't\n    // returning a partial response (ie with `PrefetchKind.AUTO`).\n    // In the auto case, since loading.js & layout.js won't have access to search params,\n    // we can safely re-use that cache entry. But for full prefetches, we should not\n    // re-use the cache entry as the response may differ.\n    if (includeSearchParams) {\n        // if we have a full prefetch, we can include the search param in the key,\n        // as we'll be getting back a full response. The server might have read the search\n        // params when generating the full response.\n        pathnameFromUrl += url.search;\n    }\n    if (prefix) {\n        return \"\" + prefix + INTERCEPTION_CACHE_KEY_MARKER + pathnameFromUrl;\n    }\n    return pathnameFromUrl;\n}\nfunction createPrefetchCacheKey(url, kind, nextUrl) {\n    return createPrefetchCacheKeyImpl(url, kind === _routerreducertypes.PrefetchKind.FULL, nextUrl);\n}\nfunction getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing) {\n    if (kind === void 0) kind = _routerreducertypes.PrefetchKind.TEMPORARY;\n    // We first check if there's a more specific interception route prefetch entry\n    // This is because when we detect a prefetch that corresponds with an interception route, we prefix it with nextUrl (see `createPrefetchCacheKey`)\n    // to avoid conflicts with other pages that may have the same URL but render different things depending on the `Next-URL` header.\n    for (const maybeNextUrl of [\n        nextUrl,\n        null\n    ]){\n        const cacheKeyWithParams = createPrefetchCacheKeyImpl(url, true, maybeNextUrl);\n        const cacheKeyWithoutParams = createPrefetchCacheKeyImpl(url, false, maybeNextUrl);\n        // First, we check if we have a cache entry that exactly matches the URL\n        const cacheKeyToUse = url.search ? cacheKeyWithParams : cacheKeyWithoutParams;\n        const existingEntry = prefetchCache.get(cacheKeyToUse);\n        if (existingEntry && allowAliasing) {\n            // We know we're returning an aliased entry when the pathname matches but the search params don't,\n            const isAliased = existingEntry.url.pathname === url.pathname && existingEntry.url.search !== url.search;\n            if (isAliased) {\n                return {\n                    ...existingEntry,\n                    aliased: true\n                };\n            }\n            return existingEntry;\n        }\n        // If the request contains search params, and we're not doing a full prefetch, we can return the\n        // param-less entry if it exists.\n        // This is technically covered by the check at the bottom of this function, which iterates over cache entries,\n        // but lets us arrive there quicker in the param-full case.\n        const entryWithoutParams = prefetchCache.get(cacheKeyWithoutParams);\n        if (false) {}\n    }\n    // If we've gotten to this point, we didn't find a specific cache entry that matched\n    // the request URL.\n    // We attempt a partial match by checking if there's a cache entry with the same pathname.\n    // Regardless of what we find, since it doesn't correspond with the requested URL, we'll mark it \"aliased\".\n    // This will signal to the router that it should only apply the loading state on the prefetched data.\n    if (false) {}\n    return undefined;\n}\nfunction getOrCreatePrefetchCacheEntry(param) {\n    let { url, nextUrl, tree, prefetchCache, kind, allowAliasing = true } = param;\n    const existingCacheEntry = getExistingCacheEntry(url, kind, nextUrl, prefetchCache, allowAliasing);\n    if (existingCacheEntry) {\n        // Grab the latest status of the cache entry and update it\n        existingCacheEntry.status = getPrefetchEntryCacheStatus(existingCacheEntry);\n        // when `kind` is provided, an explicit prefetch was requested.\n        // if the requested prefetch is \"full\" and the current cache entry wasn't, we want to re-prefetch with the new intent\n        const switchedToFullPrefetch = existingCacheEntry.kind !== _routerreducertypes.PrefetchKind.FULL && kind === _routerreducertypes.PrefetchKind.FULL;\n        if (switchedToFullPrefetch) {\n            // If we switched to a full prefetch, validate that the existing cache entry contained partial data.\n            // It's possible that the cache entry was seeded with full data but has a cache type of \"auto\" (ie when cache entries\n            // are seeded but without a prefetch intent)\n            existingCacheEntry.data.then((prefetchResponse)=>{\n                const isFullPrefetch = Array.isArray(prefetchResponse.flightData) && prefetchResponse.flightData.some((flightData)=>{\n                    // If we started rendering from the root and we returned RSC data (seedData), we already had a full prefetch.\n                    return flightData.isRootRender && flightData.seedData !== null;\n                });\n                if (!isFullPrefetch) {\n                    return createLazyPrefetchEntry({\n                        tree,\n                        url,\n                        nextUrl,\n                        prefetchCache,\n                        // If we didn't get an explicit prefetch kind, we want to set a temporary kind\n                        // rather than assuming the same intent as the previous entry, to be consistent with how we\n                        // lazily create prefetch entries when intent is left unspecified.\n                        kind: kind != null ? kind : _routerreducertypes.PrefetchKind.TEMPORARY\n                    });\n                }\n            });\n        }\n        // If the existing cache entry was marked as temporary, it means it was lazily created when attempting to get an entry,\n        // where we didn't have the prefetch intent. Now that we have the intent (in `kind`), we want to update the entry to the more accurate kind.\n        if (kind && existingCacheEntry.kind === _routerreducertypes.PrefetchKind.TEMPORARY) {\n            existingCacheEntry.kind = kind;\n        }\n        // We've determined that the existing entry we found is still valid, so we return it.\n        return existingCacheEntry;\n    }\n    // If we didn't return an entry, create a new one.\n    return createLazyPrefetchEntry({\n        tree,\n        url,\n        nextUrl,\n        prefetchCache,\n        kind: kind || _routerreducertypes.PrefetchKind.TEMPORARY\n    });\n}\n/*\n * Used to take an existing cache entry and prefix it with the nextUrl, if it exists.\n * This ensures that we don't have conflicting cache entries for the same URL (as is the case with route interception).\n */ function prefixExistingPrefetchCacheEntry(param) {\n    let { url, nextUrl, prefetchCache, existingCacheKey } = param;\n    const existingCacheEntry = prefetchCache.get(existingCacheKey);\n    if (!existingCacheEntry) {\n        // no-op -- there wasn't an entry to move\n        return;\n    }\n    const newCacheKey = createPrefetchCacheKey(url, existingCacheEntry.kind, nextUrl);\n    prefetchCache.set(newCacheKey, {\n        ...existingCacheEntry,\n        key: newCacheKey\n    });\n    prefetchCache.delete(existingCacheKey);\n    return newCacheKey;\n}\nfunction createSeededPrefetchCacheEntry(param) {\n    let { nextUrl, tree, prefetchCache, url, data, kind } = param;\n    // The initial cache entry technically includes full data, but it isn't explicitly prefetched -- we just seed the\n    // prefetch cache so that we can skip an extra prefetch request later, since we already have the data.\n    // if the prefetch corresponds with an interception route, we use the nextUrl to prefix the cache key\n    const prefetchCacheKey = data.couldBeIntercepted ? createPrefetchCacheKey(url, kind, nextUrl) : createPrefetchCacheKey(url, kind);\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data: Promise.resolve(data),\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: Date.now(),\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\n/**\n * Creates a prefetch entry entry and enqueues a fetch request to retrieve the data.\n */ function createLazyPrefetchEntry(param) {\n    let { url, kind, tree, nextUrl, prefetchCache } = param;\n    const prefetchCacheKey = createPrefetchCacheKey(url, kind);\n    // initiates the fetch request for the prefetch and attaches a listener\n    // to the promise to update the prefetch cache entry when the promise resolves (if necessary)\n    const data = _prefetchreducer.prefetchQueue.enqueue(()=>(0, _fetchserverresponse.fetchServerResponse)(url, {\n            flightRouterState: tree,\n            nextUrl,\n            prefetchKind: kind\n        }).then((prefetchResponse)=>{\n            // TODO: `fetchServerResponse` should be more tighly coupled to these prefetch cache operations\n            // to avoid drift between this cache key prefixing logic\n            // (which is currently directly influenced by the server response)\n            let newCacheKey;\n            if (prefetchResponse.couldBeIntercepted) {\n                // Determine if we need to prefix the cache key with the nextUrl\n                newCacheKey = prefixExistingPrefetchCacheEntry({\n                    url,\n                    existingCacheKey: prefetchCacheKey,\n                    nextUrl,\n                    prefetchCache\n                });\n            }\n            // If the prefetch was a cache hit, we want to update the existing cache entry to reflect that it was a full prefetch.\n            // This is because we know that a static response will contain the full RSC payload, and can be updated to respect the `static`\n            // staleTime.\n            if (prefetchResponse.prerendered) {\n                const existingCacheEntry = prefetchCache.get(newCacheKey != null ? newCacheKey : prefetchCacheKey);\n                if (existingCacheEntry) {\n                    existingCacheEntry.kind = _routerreducertypes.PrefetchKind.FULL;\n                    if (prefetchResponse.staleTime !== -1) {\n                        // This is the stale time that was collected by the server during\n                        // static generation. Use this in place of the default stale time.\n                        existingCacheEntry.staleTime = prefetchResponse.staleTime;\n                    }\n                }\n            }\n            return prefetchResponse;\n        }));\n    const prefetchEntry = {\n        treeAtTimeOfPrefetch: tree,\n        data,\n        kind,\n        prefetchTime: Date.now(),\n        lastUsedTime: null,\n        staleTime: -1,\n        key: prefetchCacheKey,\n        status: _routerreducertypes.PrefetchCacheEntryStatus.fresh,\n        url\n    };\n    prefetchCache.set(prefetchCacheKey, prefetchEntry);\n    return prefetchEntry;\n}\nfunction prunePrefetchCache(prefetchCache) {\n    for (const [href, prefetchCacheEntry] of prefetchCache){\n        if (getPrefetchEntryCacheStatus(prefetchCacheEntry) === _routerreducertypes.PrefetchCacheEntryStatus.expired) {\n            prefetchCache.delete(href);\n        }\n    }\n}\nconst DYNAMIC_STALETIME_MS = Number(\"0\") * 1000;\nconst STATIC_STALETIME_MS = Number(\"300\") * 1000;\nfunction getPrefetchEntryCacheStatus(param) {\n    let { kind, prefetchTime, lastUsedTime, staleTime } = param;\n    if (staleTime !== -1) {\n        // `staleTime` is the value sent by the server during static generation.\n        // When this is available, it takes precedence over any of the heuristics\n        // that follow.\n        //\n        // TODO: When PPR is enabled, the server will *always* return a stale time\n        // when prefetching. We should never use a prefetch entry that hasn't yet\n        // received data from the server. So the only two cases should be 1) we use\n        // the server-generated stale time 2) the unresolved entry is discarded.\n        return Date.now() < prefetchTime + staleTime ? _routerreducertypes.PrefetchCacheEntryStatus.fresh : _routerreducertypes.PrefetchCacheEntryStatus.stale;\n    }\n    // We will re-use the cache entry data for up to the `dynamic` staletime window.\n    if (Date.now() < (lastUsedTime != null ? lastUsedTime : prefetchTime) + DYNAMIC_STALETIME_MS) {\n        return lastUsedTime ? _routerreducertypes.PrefetchCacheEntryStatus.reusable : _routerreducertypes.PrefetchCacheEntryStatus.fresh;\n    }\n    // For \"auto\" prefetching, we'll re-use only the loading boundary for up to `static` staletime window.\n    // A stale entry will only re-use the `loading` boundary, not the full data.\n    // This will trigger a \"lazy fetch\" for the full data.\n    if (kind === _routerreducertypes.PrefetchKind.AUTO) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.stale;\n        }\n    }\n    // for \"full\" prefetching, we'll re-use the cache entry data for up to `static` staletime window.\n    if (kind === _routerreducertypes.PrefetchKind.FULL) {\n        if (Date.now() < prefetchTime + STATIC_STALETIME_MS) {\n            return _routerreducertypes.PrefetchCacheEntryStatus.reusable;\n        }\n    }\n    return _routerreducertypes.PrefetchCacheEntryStatus.expired;\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=prefetch-cache-utils.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js ***!
  \*************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"hmrRefreshReducer\", ({\n    enumerable: true,\n    get: function() {\n        return hmrRefreshReducer;\n    }\n}));\nconst _fetchserverresponse = __webpack_require__(/*! ../fetch-server-response */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js\");\nconst _createhreffromurl = __webpack_require__(/*! ../create-href-from-url */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/create-href-from-url.js\");\nconst _applyrouterstatepatchtotree = __webpack_require__(/*! ../apply-router-state-patch-to-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-router-state-patch-to-tree.js\");\nconst _isnavigatingtonewrootlayout = __webpack_require__(/*! ../is-navigating-to-new-root-layout */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/is-navigating-to-new-root-layout.js\");\nconst _navigatereducer = __webpack_require__(/*! ./navigate-reducer */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js\");\nconst _handlemutable = __webpack_require__(/*! ../handle-mutable */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-mutable.js\");\nconst _applyflightdata = __webpack_require__(/*! ../apply-flight-data */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/apply-flight-data.js\");\nconst _approuter = __webpack_require__(/*! ../../app-router */ \"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\");\nconst _handlesegmentmismatch = __webpack_require__(/*! ../handle-segment-mismatch */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/handle-segment-mismatch.js\");\nconst _hasinterceptionrouteincurrenttree = __webpack_require__(/*! ./has-interception-route-in-current-tree */ \"(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/has-interception-route-in-current-tree.js\");\n// A version of refresh reducer that keeps the cache around instead of wiping all of it.\nfunction hmrRefreshReducerImpl(state, action) {\n    const { origin } = action;\n    const mutable = {};\n    const href = state.canonicalUrl;\n    mutable.preserveCustomHistoryState = false;\n    const cache = (0, _approuter.createEmptyCacheNode)();\n    // If the current tree was intercepted, the nextUrl should be included in the request.\n    // This is to ensure that the refresh request doesn't get intercepted, accidentally triggering the interception route.\n    const includeNextUrl = (0, _hasinterceptionrouteincurrenttree.hasInterceptionRouteInCurrentTree)(state.tree);\n    // TODO-APP: verify that `href` is not an external url.\n    // Fetch data from the root of the tree.\n    const navigatedAt = Date.now();\n    cache.lazyData = (0, _fetchserverresponse.fetchServerResponse)(new URL(href, origin), {\n        flightRouterState: [\n            state.tree[0],\n            state.tree[1],\n            state.tree[2],\n            'refetch'\n        ],\n        nextUrl: includeNextUrl ? state.nextUrl : null,\n        isHmrRefresh: true\n    });\n    return cache.lazyData.then((param)=>{\n        let { flightData, canonicalUrl: canonicalUrlOverride } = param;\n        // Handle case when navigating to page in `pages` from `app`\n        if (typeof flightData === 'string') {\n            return (0, _navigatereducer.handleExternalUrl)(state, mutable, flightData, state.pushRef.pendingPush);\n        }\n        // Remove cache.lazyData as it has been resolved at this point.\n        cache.lazyData = null;\n        let currentTree = state.tree;\n        let currentCache = state.cache;\n        for (const normalizedFlightData of flightData){\n            const { tree: treePatch, isRootRender } = normalizedFlightData;\n            if (!isRootRender) {\n                // TODO-APP: handle this case better\n                console.log('REFRESH FAILED');\n                return state;\n            }\n            const newTree = (0, _applyrouterstatepatchtotree.applyRouterStatePatchToTree)([\n                ''\n            ], currentTree, treePatch, state.canonicalUrl);\n            if (newTree === null) {\n                return (0, _handlesegmentmismatch.handleSegmentMismatch)(state, action, treePatch);\n            }\n            if ((0, _isnavigatingtonewrootlayout.isNavigatingToNewRootLayout)(currentTree, newTree)) {\n                return (0, _navigatereducer.handleExternalUrl)(state, mutable, href, state.pushRef.pendingPush);\n            }\n            const canonicalUrlOverrideHref = canonicalUrlOverride ? (0, _createhreffromurl.createHrefFromUrl)(canonicalUrlOverride) : undefined;\n            if (canonicalUrlOverride) {\n                mutable.canonicalUrl = canonicalUrlOverrideHref;\n            }\n            const applied = (0, _applyflightdata.applyFlightData)(navigatedAt, currentCache, cache, normalizedFlightData);\n            if (applied) {\n                mutable.cache = cache;\n                currentCache = cache;\n            }\n            mutable.patchedTree = newTree;\n            mutable.canonicalUrl = href;\n            currentTree = newTree;\n        }\n        return (0, _handlemutable.handleMutable)(state, mutable);\n    }, ()=>state);\n}\nfunction hmrRefreshReducerNoop(state, _action) {\n    return state;\n}\nconst hmrRefreshReducer =  false ? 0 : hmrRefreshReducerImpl;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=hmr-refresh-reducer.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/hmr-refresh-reducer.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/client/components/use-action-queue.js ***!
  \**********************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    dispatchAppRouterAction: function() {\n        return dispatchAppRouterAction;\n    },\n    useActionQueue: function() {\n        return useActionQueue;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _isthenable = __webpack_require__(/*! ../../shared/lib/is-thenable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/is-thenable.js\");\n// The app router state lives outside of React, so we can import the dispatch\n// method directly wherever we need it, rather than passing it around via props\n// or context.\nlet dispatch = null;\nfunction dispatchAppRouterAction(action) {\n    if (dispatch === null) {\n        throw Object.defineProperty(new Error('Internal Next.js error: Router action dispatched before initialization.'), \"__NEXT_ERROR_CODE\", {\n            value: \"E668\",\n            enumerable: false,\n            configurable: true\n        });\n    }\n    dispatch(action);\n}\nfunction useActionQueue(actionQueue) {\n    _s();\n    const [state, setState] = _react.default.useState(actionQueue.state);\n    // Because of a known issue that requires to decode Flight streams inside the\n    // render phase, we have to be a bit clever and assign the dispatch method to\n    // a module-level variable upon initialization. The useState hook in this\n    // module only exists to synchronize state that lives outside of React.\n    // Ideally, what we'd do instead is pass the state as a prop to root.render;\n    // this is conceptually how we're modeling the app router state, despite the\n    // weird implementation details.\n    if (true) {\n        const useSyncDevRenderIndicator = (__webpack_require__(/*! ./react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/utils/dev-indicator/use-sync-dev-render-indicator.js\").useSyncDevRenderIndicator);\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const syncDevRenderIndicator = useSyncDevRenderIndicator();\n        dispatch = (action)=>{\n            syncDevRenderIndicator(()=>{\n                actionQueue.dispatch(action, setState);\n            });\n        };\n    } else {}\n    return (0, _isthenable.isThenable)(state) ? (0, _react.use)(state) : state;\n}\n_s(useActionQueue, \"Rp0Tj1zyE8LTecjN/cjTzn46xPo=\");\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-action-queue.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-action-queue.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js ***!
  \******************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This file is only used in app router due to the specific error state handling.\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    onCaughtError: function() {\n        return onCaughtError;\n    },\n    onUncaughtError: function() {\n        return onUncaughtError;\n    }\n});\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _useerrorhandler = __webpack_require__(/*! ../components/errors/use-error-handler */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/use-error-handler.js\");\nconst _isnextroutererror = __webpack_require__(/*! ../components/is-next-router-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/is-next-router-error.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _interceptconsoleerror = __webpack_require__(/*! ../components/globals/intercept-console-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/globals/intercept-console-error.js\");\nconst _errorboundary = __webpack_require__(/*! ../components/error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\");\nfunction onCaughtError(err, errorInfo) {\n    var _errorInfo_errorBoundary;\n    const errorBoundaryComponent = (_errorInfo_errorBoundary = errorInfo.errorBoundary) == null ? void 0 : _errorInfo_errorBoundary.constructor;\n    let isImplicitErrorBoundary;\n    if (true) {\n        const { AppDevOverlayErrorBoundary } = __webpack_require__(/*! ../components/react-dev-overlay/app/app-dev-overlay-error-boundary */ \"(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/app-dev-overlay-error-boundary.js\");\n        isImplicitErrorBoundary = errorBoundaryComponent === AppDevOverlayErrorBoundary;\n    }\n    isImplicitErrorBoundary = isImplicitErrorBoundary || errorBoundaryComponent === _errorboundary.ErrorBoundaryHandler && errorInfo.errorBoundary.props.errorComponent === _errorboundary.GlobalError;\n    if (isImplicitErrorBoundary) {\n        // We don't consider errors caught unless they're caught by an explicit error\n        // boundary. The built-in ones are considered implicit.\n        // This mimics how the same app would behave without Next.js.\n        return onUncaughtError(err, errorInfo);\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        var _errorInfo_componentStack;\n        const errorBoundaryName = (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.displayName) || (errorBoundaryComponent == null ? void 0 : errorBoundaryComponent.name) || 'Unknown';\n        const componentThatErroredFrame = errorInfo == null ? void 0 : (_errorInfo_componentStack = errorInfo.componentStack) == null ? void 0 : _errorInfo_componentStack.split('\\n')[1];\n        var // example 1: at Page (http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1)\n        // example 2: Page@http://localhost:3000/_next/static/chunks/pages/index.js?ts=1631600000000:2:1\n        _componentThatErroredFrame_match;\n        // Match chrome or safari stack trace\n        const matches = (_componentThatErroredFrame_match = componentThatErroredFrame == null ? void 0 : componentThatErroredFrame.match(/\\s+at (\\w+)\\s+|(\\w+)@/)) != null ? _componentThatErroredFrame_match : [];\n        const componentThatErroredName = matches[1] || matches[2] || 'Unknown';\n        // Create error location with errored component and error boundary, to match the behavior of default React onCaughtError handler.\n        const errorBoundaryMessage = \"It was handled by the <\" + errorBoundaryName + \"> error boundary.\";\n        const componentErrorMessage = componentThatErroredName ? \"The above error occurred in the <\" + componentThatErroredName + \"> component.\" : \"The above error occurred in one of your components.\";\n        const errorLocation = componentErrorMessage + \" \" + errorBoundaryMessage;\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // Log and report the error with location but without modifying the error stack\n        (0, _interceptconsoleerror.originConsoleError)('%o\\n\\n%s', err, errorLocation);\n        (0, _useerrorhandler.handleClientError)(stitchedError);\n    } else {}\n}\nfunction onUncaughtError(err, errorInfo) {\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(err) || (0, _isnextroutererror.isNextRouterError)(err)) return;\n    if (true) {\n        const stitchedError = (0, _stitchederror.getReactStitchedError)(err);\n        // TODO: change to passing down errorInfo later\n        // In development mode, pass along the component stack to the error\n        if (errorInfo.componentStack) {\n            ;\n            stitchedError._componentStack = errorInfo.componentStack;\n        }\n        // TODO: Add an adendum to the overlay telling people about custom error boundaries.\n        (0, _reportglobalerror.reportGlobalError)(stitchedError);\n    } else {}\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=error-boundary-callbacks.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/error-boundary-callbacks.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js ***!
  \**************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("// This module can be shared between both pages router and app router\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"onRecoverableError\", ({\n    enumerable: true,\n    get: function() {\n        return onRecoverableError;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _bailouttocsr = __webpack_require__(/*! ../../shared/lib/lazy-dynamic/bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nconst _reportglobalerror = __webpack_require__(/*! ./report-global-error */ \"(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/report-global-error.js\");\nconst _stitchederror = __webpack_require__(/*! ../components/errors/stitched-error */ \"(app-pages-browser)/./node_modules/next/dist/client/components/errors/stitched-error.js\");\nconst _iserror = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ../../lib/is-error */ \"(app-pages-browser)/./node_modules/next/dist/lib/is-error.js\"));\nconst onRecoverableError = (error, errorInfo)=>{\n    // x-ref: https://github.com/facebook/react/pull/28736\n    const cause = (0, _iserror.default)(error) && 'cause' in error ? error.cause : error;\n    const stitchedError = (0, _stitchederror.getReactStitchedError)(cause);\n    // In development mode, pass along the component stack to the error\n    if ( true && errorInfo.componentStack) {\n        ;\n        stitchedError._componentStack = errorInfo.componentStack;\n    }\n    // Skip certain custom errors which are not expected to be reported on client\n    if ((0, _bailouttocsr.isBailoutToCSRError)(cause)) return;\n    (0, _reportglobalerror.reportGlobalError)(stitchedError);\n};\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=on-recoverable-error.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/react-client-callbacks/on-recoverable-error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js":
/*!********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js ***!
  \********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    AppRouterContext: function() {\n        return AppRouterContext;\n    },\n    GlobalLayoutRouterContext: function() {\n        return GlobalLayoutRouterContext;\n    },\n    LayoutRouterContext: function() {\n        return LayoutRouterContext;\n    },\n    MissingSlotContext: function() {\n        return MissingSlotContext;\n    },\n    TemplateContext: function() {\n        return TemplateContext;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst AppRouterContext = _react.default.createContext(null);\nconst LayoutRouterContext = _react.default.createContext(null);\nconst GlobalLayoutRouterContext = _react.default.createContext(null);\nconst TemplateContext = _react.default.createContext(null);\nif (true) {\n    AppRouterContext.displayName = 'AppRouterContext';\n    LayoutRouterContext.displayName = 'LayoutRouterContext';\n    GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext';\n    TemplateContext.displayName = 'TemplateContext';\n}\nconst MissingSlotContext = _react.default.createContext(new Set()); //# sourceMappingURL=app-router-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js ***!
  \**********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    PathParamsContext: function() {\n        return PathParamsContext;\n    },\n    PathnameContext: function() {\n        return PathnameContext;\n    },\n    SearchParamsContext: function() {\n        return SearchParamsContext;\n    }\n});\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst SearchParamsContext = (0, _react.createContext)(null);\nconst PathnameContext = (0, _react.createContext)(null);\nconst PathParamsContext = (0, _react.createContext)(null);\nif (true) {\n    SearchParamsContext.displayName = 'SearchParamsContext';\n    PathnameContext.displayName = 'PathnameContext';\n    PathParamsContext.displayName = 'PathParamsContext';\n} //# sourceMappingURL=hooks-client-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9ob29rcy1jbGllbnQtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7SUFPYUEsaUJBQWlCO2VBQWpCQTs7SUFEQUMsZUFBZTtlQUFmQTs7SUFEQUMsbUJBQW1CO2VBQW5CQTs7O21DQUhpQjtBQUd2QixNQUFNQSxzQkFBc0JDLENBQUFBLEdBQUFBLE9BQUFBLGFBQUFBLEVBQXNDO0FBQ2xFLE1BQU1GLGtCQUFrQkUsQ0FBQUEsR0FBQUEsT0FBQUEsYUFBQUEsRUFBNkI7QUFDckQsTUFBTUgsb0JBQW9CRyxDQUFBQSxHQUFBQSxPQUFBQSxhQUFBQSxFQUE2QjtBQUU5RCxJQUFJQyxJQUFvQixFQUFtQjtJQUN6Q0Ysb0JBQW9CSyxXQUFXLEdBQUc7SUFDbENOLGdCQUFnQk0sV0FBVyxHQUFHO0lBQzlCUCxrQkFBa0JPLFdBQVcsR0FBRztBQUNsQyIsInNvdXJjZXMiOlsiL1VzZXJzL3NyYy9zaGFyZWQvbGliL2hvb2tzLWNsaWVudC1jb250ZXh0LnNoYXJlZC1ydW50aW1lLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgdHlwZSB7IFBhcmFtcyB9IGZyb20gJy4uLy4uL3NlcnZlci9yZXF1ZXN0L3BhcmFtcydcblxuZXhwb3J0IGNvbnN0IFNlYXJjaFBhcmFtc0NvbnRleHQgPSBjcmVhdGVDb250ZXh0PFVSTFNlYXJjaFBhcmFtcyB8IG51bGw+KG51bGwpXG5leHBvcnQgY29uc3QgUGF0aG5hbWVDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxzdHJpbmcgfCBudWxsPihudWxsKVxuZXhwb3J0IGNvbnN0IFBhdGhQYXJhbXNDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxQYXJhbXMgfCBudWxsPihudWxsKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBTZWFyY2hQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1NlYXJjaFBhcmFtc0NvbnRleHQnXG4gIFBhdGhuYW1lQ29udGV4dC5kaXNwbGF5TmFtZSA9ICdQYXRobmFtZUNvbnRleHQnXG4gIFBhdGhQYXJhbXNDb250ZXh0LmRpc3BsYXlOYW1lID0gJ1BhdGhQYXJhbXNDb250ZXh0J1xufVxuIl0sIm5hbWVzIjpbIlBhdGhQYXJhbXNDb250ZXh0IiwiUGF0aG5hbWVDb250ZXh0IiwiU2VhcmNoUGFyYW1zQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJwcm9jZXNzIiwiZW52IiwiTk9ERV9FTlYiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/hooks-client-context.shared-runtime.js\n"));

/***/ })

});