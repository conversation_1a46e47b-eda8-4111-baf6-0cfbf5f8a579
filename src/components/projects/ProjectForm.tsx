'use client'

/**
 * Project Form Component - متبرمج ERP System
 * Features: React Hook Form + Zod validation, Arabic labels, RTL layout
 */

import React from 'react'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Project, ProjectFormData, ProjectStatus, ProjectPriority, ProjectStatusOptions, ProjectPriorityOptions } from '@/types/project'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { cn } from '@/lib/utils'

// Zod validation schema
const projectSchema = z.object({
  name: z.string().min(1, 'اسم المشروع مطلوب').max(200, 'اسم المشروع طويل جداً'),
  description: z.string().optional(),
  start_date: z.string().min(1, 'تاريخ البداية مطلوب'),
  expected_end_date: z.string().min(1, 'تاريخ الانتهاء المتوقع مطلوب'),
  actual_end_date: z.string().optional(),
  status: z.nativeEnum(ProjectStatus),
  priority: z.nativeEnum(ProjectPriority),
  progress_percentage: z.number().min(0, 'النسبة لا يمكن أن تكون أقل من 0').max(100, 'النسبة لا يمكن أن تكون أكثر من 100'),
  estimated_hours: z.number().min(0, 'الساعات المقدرة لا يمكن أن تكون سالبة'),
  actual_hours: z.number().min(0, 'الساعات الفعلية لا يمكن أن تكون سالبة'),
  implementation_phases: z.array(z.string()).default([]),
  technologies_used: z.array(z.string()).default([]),
  domain_link: z.string().url('رابط غير صحيح').optional().or(z.literal('')),
  domain_email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  domain_password: z.string().optional(),
  server_link: z.string().url('رابط غير صحيح').optional().or(z.literal('')),
  server_email: z.string().email('بريد إلكتروني غير صحيح').optional().or(z.literal('')),
  server_password: z.string().optional(),
  whatsapp_group_link: z.string().url('رابط غير صحيح').optional().or(z.literal('')),
  slack_channel: z.string().optional(),
  budget: z.number().min(0, 'الميزانية لا يمكن أن تكون سالبة'),
  actual_cost: z.number().min(0, 'التكلفة الفعلية لا يمكن أن تكون سالبة'),
})

type ProjectFormValues = z.infer<typeof projectSchema>

interface ProjectFormProps {
  project?: Project
  onSubmit: (data: ProjectFormData) => void
  onCancel: () => void
  loading?: boolean
}

export function ProjectForm({ project, onSubmit, onCancel, loading = false }: ProjectFormProps) {
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<ProjectFormValues>({
    resolver: zodResolver(projectSchema),
    defaultValues: project ? {
      name: project.name,
      description: project.description || '',
      start_date: project.start_date,
      expected_end_date: project.expected_end_date,
      actual_end_date: project.actual_end_date || '',
      status: project.status,
      priority: project.priority,
      progress_percentage: project.progress_percentage,
      estimated_hours: project.estimated_hours,
      actual_hours: project.actual_hours,
      implementation_phases: project.implementation_phases || [],
      technologies_used: project.technologies_used || [],
      domain_link: project.domain_link || '',
      domain_email: project.domain_email || '',
      domain_password: project.domain_password || '',
      server_link: project.server_link || '',
      server_email: project.server_email || '',
      server_password: project.server_password || '',
      whatsapp_group_link: project.whatsapp_group_link || '',
      slack_channel: project.slack_channel || '',
      budget: project.budget,
      actual_cost: project.actual_cost,
    } : {
      name: '',
      description: '',
      start_date: '',
      expected_end_date: '',
      actual_end_date: '',
      status: ProjectStatus.PLANNING,
      priority: ProjectPriority.MEDIUM,
      progress_percentage: 0,
      estimated_hours: 0,
      actual_hours: 0,
      implementation_phases: [],
      technologies_used: [],
      domain_link: '',
      domain_email: '',
      domain_password: '',
      server_link: '',
      server_email: '',
      server_password: '',
      whatsapp_group_link: '',
      slack_channel: '',
      budget: 0,
      actual_cost: 0,
    },
  })

  const handleFormSubmit = (data: ProjectFormValues) => {
    onSubmit(data as ProjectFormData)
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">المعلومات الأساسية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="اسم المشروع *"
            {...register('name')}
            error={errors.name?.message}
            placeholder="أدخل اسم المشروع"
          />
          
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              وصف المشروع
            </label>
            <textarea
              {...register('description')}
              rows={3}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
              placeholder="أدخل وصف تفصيلي للمشروع"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Dates and Timeline */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">التواريخ والجدولة</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            label="تاريخ البداية *"
            type="date"
            {...register('start_date')}
            error={errors.start_date?.message}
          />
          
          <Input
            label="تاريخ الانتهاء المتوقع *"
            type="date"
            {...register('expected_end_date')}
            error={errors.expected_end_date?.message}
          />
          
          <Input
            label="تاريخ الانتهاء الفعلي"
            type="date"
            {...register('actual_end_date')}
            error={errors.actual_end_date?.message}
          />
        </div>
      </div>

      {/* Project Management */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">إدارة المشروع</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              حالة المشروع *
            </label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  {ProjectStatusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الأولوية *
            </label>
            <Controller
              name="priority"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
                >
                  {ProjectPriorityOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              )}
            />
            {errors.priority && (
              <p className="mt-1 text-sm text-red-600">{errors.priority.message}</p>
            )}
          </div>
          
          <Input
            label="نسبة الإنجاز (%)"
            type="number"
            min="0"
            max="100"
            {...register('progress_percentage', { valueAsNumber: true })}
            error={errors.progress_percentage?.message}
          />
        </div>
      </div>

      {/* Resources and Hours */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">الموارد والساعات</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="الساعات المقدرة"
            type="number"
            min="0"
            {...register('estimated_hours', { valueAsNumber: true })}
            error={errors.estimated_hours?.message}
          />
          
          <Input
            label="الساعات الفعلية"
            type="number"
            min="0"
            {...register('actual_hours', { valueAsNumber: true })}
            error={errors.actual_hours?.message}
          />
        </div>
      </div>

      {/* Financial */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <h3 className="text-lg font-medium text-gray-900 mb-4">المالية</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="الميزانية (ج.م)"
            type="number"
            min="0"
            step="0.01"
            {...register('budget', { valueAsNumber: true })}
            error={errors.budget?.message}
          />
          
          <Input
            label="التكلفة الفعلية (ج.م)"
            type="number"
            min="0"
            step="0.01"
            {...register('actual_cost', { valueAsNumber: true })}
            error={errors.actual_cost?.message}
          />
        </div>
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3 space-x-reverse">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          إلغاء
        </Button>
        <Button
          type="submit"
          loading={loading}
          disabled={!isValid}
        >
          {project ? 'تحديث المشروع' : 'إنشاء المشروع'}
        </Button>
      </div>
    </form>
  )
}
