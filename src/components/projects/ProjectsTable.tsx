'use client'

/**
 * Projects Table Component with TanStack Table - متبرمج ERP System
 * Features: Arabic RTL, sorting, filtering, pagination, actions
 */

import React, { useMemo } from 'react'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  flexRender,
  createColumnHelper,
  type SortingState,
  type ColumnFiltersState,
} from '@tanstack/react-table'
import { Project, ProjectStatus, ProjectPriority, ProjectStatusLabels, ProjectPriorityLabels } from '@/types/project'
import { Badge } from '@/components/ui/Badge'
import { Button } from '@/components/ui/Button'
import { formatCurrency, formatDate, getStatusColor, getPriorityColor, cn } from '@/lib/utils'
import { ChevronUpIcon, ChevronDownIcon, PencilIcon, TrashIcon, EyeIcon } from '@heroicons/react/24/outline'

interface ProjectsTableProps {
  projects: Project[]
  loading?: boolean
  onEdit?: (project: Project) => void
  onDelete?: (project: Project) => void
  onView?: (project: Project) => void
}

const columnHelper = createColumnHelper<Project>()

export function ProjectsTable({ projects, loading = false, onEdit, onDelete, onView }: ProjectsTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])

  const columns = useMemo(
    () => [
      columnHelper.accessor('name', {
        header: 'اسم المشروع',
        cell: (info) => (
          <div className="font-medium text-gray-900">
            {info.getValue()}
          </div>
        ),
        enableSorting: true,
      }),
      
      columnHelper.accessor('status', {
        header: 'الحالة',
        cell: (info) => {
          const status = info.getValue()
          return (
            <Badge className={cn('text-xs', getStatusColor(status))}>
              {ProjectStatusLabels[status as ProjectStatus]}
            </Badge>
          )
        },
        enableSorting: true,
        filterFn: 'equals',
      }),
      
      columnHelper.accessor('priority', {
        header: 'الأولوية',
        cell: (info) => {
          const priority = info.getValue()
          return (
            <Badge className={cn('text-xs', getPriorityColor(priority))}>
              {ProjectPriorityLabels[priority as ProjectPriority]}
            </Badge>
          )
        },
        enableSorting: true,
        filterFn: 'equals',
      }),
      
      columnHelper.accessor('progress_percentage', {
        header: 'نسبة الإنجاز',
        cell: (info) => {
          const progress = info.getValue()
          return (
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-16 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <span className="text-sm text-gray-600">{progress}%</span>
            </div>
          )
        },
        enableSorting: true,
      }),
      
      columnHelper.accessor('start_date', {
        header: 'تاريخ البداية',
        cell: (info) => (
          <span className="text-sm text-gray-600">
            {formatDate(info.getValue())}
          </span>
        ),
        enableSorting: true,
      }),
      
      columnHelper.accessor('expected_end_date', {
        header: 'تاريخ الانتهاء المتوقع',
        cell: (info) => {
          const date = info.getValue()
          const isOverdue = new Date(date) < new Date() && info.row.original.status !== ProjectStatus.COMPLETED
          return (
            <span className={cn('text-sm', isOverdue ? 'text-red-600 font-medium' : 'text-gray-600')}>
              {formatDate(date)}
              {isOverdue && ' (متأخر)'}
            </span>
          )
        },
        enableSorting: true,
      }),
      
      columnHelper.accessor('project_manager', {
        header: 'مدير المشروع',
        cell: (info) => {
          const manager = info.getValue()
          return manager ? (
            <span className="text-sm text-gray-600">
              {manager.first_name} {manager.last_name}
            </span>
          ) : (
            <span className="text-sm text-gray-400">غير محدد</span>
          )
        },
        enableSorting: false,
      }),
      
      columnHelper.accessor('budget', {
        header: 'الميزانية',
        cell: (info) => (
          <span className="text-sm text-gray-600">
            {formatCurrency(info.getValue())}
          </span>
        ),
        enableSorting: true,
      }),
      
      columnHelper.display({
        id: 'actions',
        header: 'الإجراءات',
        cell: (info) => (
          <div className="flex items-center space-x-2 space-x-reverse">
            {onView && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onView(info.row.original)}
                className="h-8 w-8 p-0"
              >
                <EyeIcon className="h-4 w-4" />
              </Button>
            )}
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(info.row.original)}
                className="h-8 w-8 p-0"
              >
                <PencilIcon className="h-4 w-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(info.row.original)}
                className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            )}
          </div>
        ),
      }),
    ],
    [onEdit, onDelete, onView]
  )

  const table = useReactTable({
    data: projects,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="mr-2 text-gray-600">جاري التحميل...</span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        className={cn(
                          'flex items-center space-x-1 space-x-reverse',
                          header.column.getCanSort() && 'cursor-pointer select-none hover:text-gray-700'
                        )}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        <span>
                          {flexRender(header.column.columnDef.header, header.getContext())}
                        </span>
                        {header.column.getCanSort() && (
                          <span className="flex flex-col">
                            <ChevronUpIcon
                              className={cn(
                                'h-3 w-3',
                                header.column.getIsSorted() === 'asc' ? 'text-gray-900' : 'text-gray-400'
                              )}
                            />
                            <ChevronDownIcon
                              className={cn(
                                'h-3 w-3 -mt-1',
                                header.column.getIsSorted() === 'desc' ? 'text-gray-900' : 'text-gray-400'
                              )}
                            />
                          </span>
                        )}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {table.getRowModel().rows.map((row) => (
              <tr key={row.id} className="hover:bg-gray-50">
                {row.getVisibleCells().map((cell) => (
                  <td key={cell.id} className="px-6 py-4 whitespace-nowrap text-sm">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 space-x-reverse">
          <span className="text-sm text-gray-700">
            عرض {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1} إلى{' '}
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            من {table.getFilteredRowModel().rows.length} نتيجة
          </span>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            السابق
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            التالي
          </Button>
        </div>
      </div>
    </div>
  )
}
