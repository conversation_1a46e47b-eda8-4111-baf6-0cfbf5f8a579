'use client'

/**
 * Projects Filter Component - متبرمج ERP System
 * Features: Status, Priority, Date range, Search filters
 */

import React from 'react'
import { ProjectFilters, ProjectStatusOptions, ProjectPriorityOptions } from '@/types/project'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { MagnifyingGlassIcon, FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline'

interface ProjectsFilterProps {
  filters: ProjectFilters
  onFiltersChange: (filters: Partial<ProjectFilters>) => void
  onClearFilters: () => void
}

export function ProjectsFilter({ filters, onFiltersChange, onClearFilters }: ProjectsFilterProps) {
  const [isExpanded, setIsExpanded] = React.useState(false)

  const handleFilterChange = (key: keyof ProjectFilters, value: any) => {
    onFiltersChange({ [key]: value })
  }

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  )

  return (
    <div className="bg-white p-4 rounded-lg shadow-sm border space-y-4">
      {/* Search and Toggle */}
      <div className="flex items-center space-x-4 space-x-reverse">
        <div className="flex-1">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المشاريع..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            />
          </div>
        </div>
        
        <Button
          variant="outline"
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center space-x-2 space-x-reverse"
        >
          <FunnelIcon className="h-4 w-4" />
          <span>فلترة</span>
        </Button>
        
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={onClearFilters}
            className="flex items-center space-x-2 space-x-reverse text-red-600 hover:text-red-700"
          >
            <XMarkIcon className="h-4 w-4" />
            <span>مسح الفلاتر</span>
          </Button>
        )}
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t">
          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الحالة
            </label>
            <select
              value={filters.status || ''}
              onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            >
              <option value="">جميع الحالات</option>
              {ProjectStatusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Priority Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الأولوية
            </label>
            <select
              value={filters.priority || ''}
              onChange={(e) => handleFilterChange('priority', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            >
              <option value="">جميع الأولويات</option>
              {ProjectPriorityOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Start Date From */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              تاريخ البداية من
            </label>
            <input
              type="date"
              value={filters.start_date_from || ''}
              onChange={(e) => handleFilterChange('start_date_from', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            />
          </div>

          {/* Start Date To */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              تاريخ البداية إلى
            </label>
            <input
              type="date"
              value={filters.start_date_to || ''}
              onChange={(e) => handleFilterChange('start_date_to', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            />
          </div>

          {/* Expected End Date From */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              تاريخ الانتهاء المتوقع من
            </label>
            <input
              type="date"
              value={filters.expected_end_date_from || ''}
              onChange={(e) => handleFilterChange('expected_end_date_from', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            />
          </div>

          {/* Expected End Date To */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              تاريخ الانتهاء المتوقع إلى
            </label>
            <input
              type="date"
              value={filters.expected_end_date_to || ''}
              onChange={(e) => handleFilterChange('expected_end_date_to', e.target.value || undefined)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            />
          </div>

          {/* Ordering */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ترتيب حسب
            </label>
            <select
              value={filters.ordering || '-start_date'}
              onChange={(e) => handleFilterChange('ordering', e.target.value)}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            >
              <option value="-start_date">تاريخ البداية (الأحدث أولاً)</option>
              <option value="start_date">تاريخ البداية (الأقدم أولاً)</option>
              <option value="-expected_end_date">تاريخ الانتهاء (الأحدث أولاً)</option>
              <option value="expected_end_date">تاريخ الانتهاء (الأقدم أولاً)</option>
              <option value="name">الاسم (أ-ي)</option>
              <option value="-name">الاسم (ي-أ)</option>
              <option value="-priority">الأولوية (عالية أولاً)</option>
              <option value="priority">الأولوية (منخفضة أولاً)</option>
              <option value="-progress_percentage">نسبة الإنجاز (عالية أولاً)</option>
              <option value="progress_percentage">نسبة الإنجاز (منخفضة أولاً)</option>
            </select>
          </div>

          {/* Page Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              عدد النتائج في الصفحة
            </label>
            <select
              value={filters.page_size || 25}
              onChange={(e) => handleFilterChange('page_size', parseInt(e.target.value))}
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500"
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
        </div>
      )}

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 pt-2 border-t">
          <span className="text-sm text-gray-600">الفلاتر النشطة:</span>
          {filters.status && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
              الحالة: {ProjectStatusOptions.find(opt => opt.value === filters.status)?.label}
            </span>
          )}
          {filters.priority && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
              الأولوية: {ProjectPriorityOptions.find(opt => opt.value === filters.priority)?.label}
            </span>
          )}
          {filters.search && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
              البحث: {filters.search}
            </span>
          )}
          {filters.start_date_from && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
              من: {filters.start_date_from}
            </span>
          )}
          {filters.start_date_to && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
              إلى: {filters.start_date_to}
            </span>
          )}
        </div>
      )}
    </div>
  )
}
