'use client'

/**
 * Sidebar Navigation Component - متبرمج ERP System
 * Arabic RTL sidebar with navigation menu
 */

import React from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  HomeIcon,
  FolderIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  CogIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'الرئيسية', href: '/', icon: HomeIcon },
  { name: 'المشاريع', href: '/projects', icon: FolderIcon },
  { name: 'العملاء', href: '/clients', icon: UsersIcon },
  { name: 'المهام', href: '/tasks', icon: ClipboardDocumentListIcon },
  { name: 'فريق المبيعات', href: '/sales-team', icon: UserGroupIcon },
  { name: 'مشتري الإعلانات', href: '/media-buyers', icon: ChartBarIcon },
  { name: 'المطورين', href: '/developers', icon: CogIcon },
  { name: 'المصممين', href: '/designers', icon: DocumentTextIcon },
  { name: 'مطوري ووردبريس', href: '/wordpress-developers', icon: CogIcon },
  { name: 'التجديدات السنوية', href: '/annual-renewals', icon: CalendarIcon },
  { name: 'الباقات', href: '/packages', icon: FolderIcon },
  { name: 'المصروفات', href: '/expenses', icon: CurrencyDollarIcon },
  { name: 'التقارير', href: '/reports', icon: ChartBarIcon },
  { name: 'إعدادات النظام', href: '/settings', icon: CogIcon },
]

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  const pathname = usePathname()

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-25 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
          isOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-blue-600">
            <Link href="/" className="flex items-center space-x-2 space-x-reverse">
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold text-lg">م</span>
              </div>
              <span className="text-white font-bold text-xl">متبرمج</span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                  )}
                  onClick={() => onClose()}
                >
                  <item.icon
                    className={cn(
                      'ml-3 h-5 w-5 flex-shrink-0',
                      isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                    )}
                  />
                  {item.name}
                </Link>
              )
            })}
          </nav>

          {/* User Info */}
          <div className="flex-shrink-0 p-4 border-t border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-medium text-sm">أ</span>
              </div>
              <div className="mr-3">
                <p className="text-sm font-medium text-gray-700">المدير</p>
                <p className="text-xs text-gray-500"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
