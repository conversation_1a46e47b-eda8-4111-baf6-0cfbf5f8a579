/**
 * Zustand store for Projects management - متبرمج ERP System
 */

import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Project, ProjectFilters, ProjectListResponse } from '@/types/project'
import { projectsApi } from '@/lib/api'

interface ProjectState {
  // Data
  projects: Project[]
  currentProject: Project | null
  totalCount: number
  
  // UI State
  loading: boolean
  error: string | null
  filters: ProjectFilters
  
  // Actions
  fetchProjects: (filters?: ProjectFilters) => Promise<void>
  fetchProject: (id: number) => Promise<void>
  createProject: (data: any) => Promise<Project>
  updateProject: (id: number, data: any) => Promise<Project>
  deleteProject: (id: number) => Promise<void>
  setFilters: (filters: Partial<ProjectFilters>) => void
  clearFilters: () => void
  setCurrentProject: (project: Project | null) => void
  clearError: () => void
}

const initialFilters: ProjectFilters = {
  page: 1,
  page_size: 25,
  ordering: '-start_date',
}

export const useProjectStore = create<ProjectState>()(
  devtools(
    (set, get) => ({
      // Initial state
      projects: [],
      currentProject: null,
      totalCount: 0,
      loading: false,
      error: null,
      filters: initialFilters,

      // Fetch projects with filters
      fetchProjects: async (filters?: ProjectFilters) => {
        set({ loading: true, error: null })
        
        try {
          const currentFilters = filters || get().filters
          
          // Convert filters to API params
          const params: Record<string, string> = {}
          
          if (currentFilters.status) params.status = currentFilters.status
          if (currentFilters.priority) params.priority = currentFilters.priority
          if (currentFilters.project_manager) params.project_manager = currentFilters.project_manager.toString()
          if (currentFilters.start_date_from) params.start_date_from = currentFilters.start_date_from
          if (currentFilters.start_date_to) params.start_date_to = currentFilters.start_date_to
          if (currentFilters.expected_end_date_from) params.expected_end_date_from = currentFilters.expected_end_date_from
          if (currentFilters.expected_end_date_to) params.expected_end_date_to = currentFilters.expected_end_date_to
          if (currentFilters.search) params.search = currentFilters.search
          if (currentFilters.ordering) params.ordering = currentFilters.ordering
          if (currentFilters.page) params.page = currentFilters.page.toString()
          if (currentFilters.page_size) params.page_size = currentFilters.page_size.toString()

          const response: ProjectListResponse = await projectsApi.getAll(params)
          
          set({
            projects: response.results,
            totalCount: response.count,
            loading: false,
            filters: currentFilters,
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'حدث خطأ في تحميل المشاريع',
          })
        }
      },

      // Fetch single project
      fetchProject: async (id: number) => {
        set({ loading: true, error: null })
        
        try {
          const project: Project = await projectsApi.getById(id)
          set({
            currentProject: project,
            loading: false,
          })
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'حدث خطأ في تحميل المشروع',
          })
        }
      },

      // Create new project
      createProject: async (data: any) => {
        set({ loading: true, error: null })
        
        try {
          const project: Project = await projectsApi.create(data)
          
          // Add to projects list
          set((state) => ({
            projects: [project, ...state.projects],
            totalCount: state.totalCount + 1,
            loading: false,
          }))
          
          return project
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'حدث خطأ في إنشاء المشروع',
          })
          throw error
        }
      },

      // Update project
      updateProject: async (id: number, data: any) => {
        set({ loading: true, error: null })
        
        try {
          const project: Project = await projectsApi.update(id, data)
          
          // Update in projects list
          set((state) => ({
            projects: state.projects.map((p) => (p.id === id ? project : p)),
            currentProject: state.currentProject?.id === id ? project : state.currentProject,
            loading: false,
          }))
          
          return project
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'حدث خطأ في تحديث المشروع',
          })
          throw error
        }
      },

      // Delete project
      deleteProject: async (id: number) => {
        set({ loading: true, error: null })
        
        try {
          await projectsApi.delete(id)
          
          // Remove from projects list
          set((state) => ({
            projects: state.projects.filter((p) => p.id !== id),
            totalCount: state.totalCount - 1,
            currentProject: state.currentProject?.id === id ? null : state.currentProject,
            loading: false,
          }))
        } catch (error: any) {
          set({
            loading: false,
            error: error.message || 'حدث خطأ في حذف المشروع',
          })
          throw error
        }
      },

      // Set filters
      setFilters: (newFilters: Partial<ProjectFilters>) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters },
        }))
      },

      // Clear filters
      clearFilters: () => {
        set({ filters: initialFilters })
      },

      // Set current project
      setCurrentProject: (project: Project | null) => {
        set({ currentProject: project })
      },

      // Clear error
      clearError: () => {
        set({ error: null })
      },
    }),
    {
      name: 'project-store',
    }
  )
)
