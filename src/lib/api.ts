/**
 * API Client Configuration for متبرمج ERP System
 * Handles communication with Django REST API backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8000/api'

/**
 * API Response wrapper type
 */
export interface ApiResponse<T> {
  data: T
  message?: string
  status: number
}

/**
 * API Error type
 */
export interface ApiError {
  message: string
  status: number
  details?: Record<string, string[]>
}

/**
 * Generic API client class
 */
class ApiClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('access_token')
    }
  }

  /**
   * Set authentication token
   */
  setToken(token: string) {
    this.token = token
    if (typeof window !== 'undefined') {
      localStorage.setItem('access_token', token)
    }
  }

  /**
   * Clear authentication token
   */
  clearToken() {
    this.token = null
    if (typeof window !== 'undefined') {
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  /**
   * Get default headers
   */
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    }

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`
    }

    return headers
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw {
        message: errorData.message || 'حدث خطأ في الخادم',
        status: response.status,
        details: errorData.details || errorData,
      } as ApiError
    }

    return response.json()
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, params?: Record<string, string>): Promise<T> {
    const url = new URL(`${this.baseURL}${endpoint}`)
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value)
      })
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: this.getHeaders(),
    })

    return this.handleResponse<T>(response)
  }

  /**
   * POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    })

    return this.handleResponse<T>(response)
  }

  /**
   * PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    })

    return this.handleResponse<T>(response)
  }

  /**
   * PATCH request
   */
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PATCH',
      headers: this.getHeaders(),
      body: data ? JSON.stringify(data) : undefined,
    })

    return this.handleResponse<T>(response)
  }

  /**
   * DELETE request
   */
  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    })

    return this.handleResponse<T>(response)
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL)

/**
 * Authentication API methods
 */
export const authApi = {
  login: async (credentials: { username: string; password: string }) => {
    const response = await apiClient.post<{
      access: string
      refresh: string
      user: any
    }>('/auth/token/', credentials)
    
    // Store tokens
    apiClient.setToken(response.access)
    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', response.refresh)
    }
    
    return response
  },

  refresh: async () => {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refresh_token') 
      : null
    
    if (!refreshToken) {
      throw new Error('لا يوجد رمز تحديث')
    }

    const response = await apiClient.post<{ access: string }>('/auth/token/refresh/', {
      refresh: refreshToken
    })
    
    apiClient.setToken(response.access)
    return response
  },

  logout: () => {
    apiClient.clearToken()
  }
}

/**
 * Projects API methods
 */
export const projectsApi = {
  getAll: (params?: Record<string, string>) => 
    apiClient.get<any>('/projects/', params),
  
  getById: (id: number) => 
    apiClient.get<any>(`/projects/${id}/`),
  
  create: (data: any) => 
    apiClient.post<any>('/projects/', data),
  
  update: (id: number, data: any) => 
    apiClient.put<any>(`/projects/${id}/`, data),
  
  partialUpdate: (id: number, data: any) => 
    apiClient.patch<any>(`/projects/${id}/`, data),
  
  delete: (id: number) => 
    apiClient.delete<any>(`/projects/${id}/`),
}
