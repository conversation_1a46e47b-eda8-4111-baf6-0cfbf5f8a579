/**
 * React Query hooks for Projects API - متبرمج ERP System
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { projectsApi } from '@/lib/api'
import { Project, ProjectFilters, ProjectFormData, ProjectListResponse } from '@/types/project'
import toast from 'react-hot-toast'

// Query Keys
export const projectsKeys = {
  all: ['projects'] as const,
  lists: () => [...projectsKeys.all, 'list'] as const,
  list: (filters: ProjectFilters) => [...projectsKeys.lists(), filters] as const,
  details: () => [...projectsKeys.all, 'detail'] as const,
  detail: (id: number) => [...projectsKeys.details(), id] as const,
}

/**
 * Hook to fetch projects list with filters
 */
export function useProjects(filters: ProjectFilters = {}) {
  return useQuery({
    queryKey: projectsKeys.list(filters),
    queryFn: () => {
      // Convert filters to API params
      const params: Record<string, string> = {}
      
      if (filters.status) params.status = filters.status
      if (filters.priority) params.priority = filters.priority
      if (filters.project_manager) params.project_manager = filters.project_manager.toString()
      if (filters.start_date_from) params.start_date_from = filters.start_date_from
      if (filters.start_date_to) params.start_date_to = filters.start_date_to
      if (filters.expected_end_date_from) params.expected_end_date_from = filters.expected_end_date_from
      if (filters.expected_end_date_to) params.expected_end_date_to = filters.expected_end_date_to
      if (filters.search) params.search = filters.search
      if (filters.ordering) params.ordering = filters.ordering
      if (filters.page) params.page = filters.page.toString()
      if (filters.page_size) params.page_size = filters.page_size.toString()

      return projectsApi.getAll(params) as Promise<ProjectListResponse>
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook to fetch single project
 */
export function useProject(id: number) {
  return useQuery({
    queryKey: projectsKeys.detail(id),
    queryFn: () => projectsApi.getById(id) as Promise<Project>,
    enabled: !!id,
  })
}

/**
 * Hook to create new project
 */
export function useCreateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: ProjectFormData) => projectsApi.create(data) as Promise<Project>,
    onSuccess: (newProject) => {
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: projectsKeys.lists() })
      
      // Add to cache
      queryClient.setQueryData(projectsKeys.detail(newProject.id), newProject)
      
      toast.success('تم إنشاء المشروع بنجاح')
    },
    onError: (error: any) => {
      toast.error(error.message || 'حدث خطأ في إنشاء المشروع')
    },
  })
}

/**
 * Hook to update project
 */
export function useUpdateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProjectFormData> }) =>
      projectsApi.update(id, data) as Promise<Project>,
    onSuccess: (updatedProject) => {
      // Update in cache
      queryClient.setQueryData(projectsKeys.detail(updatedProject.id), updatedProject)
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: projectsKeys.lists() })
      
      toast.success('تم تحديث المشروع بنجاح')
    },
    onError: (error: any) => {
      toast.error(error.message || 'حدث خطأ في تحديث المشروع')
    },
  })
}

/**
 * Hook to partially update project
 */
export function usePartialUpdateProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProjectFormData> }) =>
      projectsApi.partialUpdate(id, data) as Promise<Project>,
    onSuccess: (updatedProject) => {
      // Update in cache
      queryClient.setQueryData(projectsKeys.detail(updatedProject.id), updatedProject)
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: projectsKeys.lists() })
      
      toast.success('تم تحديث المشروع بنجاح')
    },
    onError: (error: any) => {
      toast.error(error.message || 'حدث خطأ في تحديث المشروع')
    },
  })
}

/**
 * Hook to delete project
 */
export function useDeleteProject() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => projectsApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: projectsKeys.detail(deletedId) })
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: projectsKeys.lists() })
      
      toast.success('تم حذف المشروع بنجاح')
    },
    onError: (error: any) => {
      toast.error(error.message || 'حدث خطأ في حذف المشروع')
    },
  })
}
