/**
 * TypeScript types for Project module - متبرمج ERP System
 */

export enum ProjectStatus {
  PLANNING = 'planning',
  IN_PROGRESS = 'in_progress',
  TESTING = 'testing',
  COMPLETED = 'completed',
  ON_HOLD = 'on_hold',
  CANCELLED = 'cancelled',
}

export enum ProjectPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export interface User {
  id: number
  username: string
  first_name: string
  last_name: string
  email: string
}

export interface Project {
  id: number
  
  // Basic Information
  name: string
  description: string
  
  // Dates and Timeline
  start_date: string // ISO date string
  expected_end_date: string // ISO date string
  actual_end_date?: string | null // ISO date string
  
  // Project Management
  status: ProjectStatus
  priority: ProjectPriority
  progress_percentage: number
  
  // Team and Resources
  project_manager?: User | null
  estimated_hours: number
  actual_hours: number
  
  // Technical Details
  implementation_phases: string[]
  technologies_used: string[]
  
  // Domain and Server Information
  domain_link?: string | null
  domain_email: string
  domain_password: string
  server_link?: string | null
  server_email: string
  server_password: string
  
  // Communication
  whatsapp_group_link?: string | null
  slack_channel: string
  
  // Financial
  budget: number
  actual_cost: number
  
  // Metadata
  created_at: string // ISO datetime string
  updated_at: string // ISO datetime string
  created_by?: User | null
  
  // Computed properties (from Django model)
  is_overdue?: boolean
  days_remaining?: number
  budget_utilization?: number
  hours_utilization?: number
}

export interface ProjectFormData {
  name: string
  description: string
  start_date: string
  expected_end_date: string
  actual_end_date?: string
  status: ProjectStatus
  priority: ProjectPriority
  progress_percentage: number
  project_manager?: number | null
  estimated_hours: number
  actual_hours: number
  implementation_phases: string[]
  technologies_used: string[]
  domain_link?: string
  domain_email: string
  domain_password: string
  server_link?: string
  server_email: string
  server_password: string
  whatsapp_group_link?: string
  slack_channel: string
  budget: number
  actual_cost: number
}

export interface ProjectFilters {
  status?: ProjectStatus
  priority?: ProjectPriority
  project_manager?: number
  start_date_from?: string
  start_date_to?: string
  expected_end_date_from?: string
  expected_end_date_to?: string
  search?: string
  ordering?: string
  page?: number
  page_size?: number
}

export interface ProjectListResponse {
  count: number
  next?: string | null
  previous?: string | null
  results: Project[]
}

// Status and Priority display labels in Arabic
export const ProjectStatusLabels: Record<ProjectStatus, string> = {
  [ProjectStatus.PLANNING]: 'التخطيط',
  [ProjectStatus.IN_PROGRESS]: 'قيد التنفيذ',
  [ProjectStatus.TESTING]: 'الاختبار',
  [ProjectStatus.COMPLETED]: 'مكتمل',
  [ProjectStatus.ON_HOLD]: 'متوقف مؤقتاً',
  [ProjectStatus.CANCELLED]: 'ملغي',
}

export const ProjectPriorityLabels: Record<ProjectPriority, string> = {
  [ProjectPriority.LOW]: 'منخفضة',
  [ProjectPriority.MEDIUM]: 'متوسطة',
  [ProjectPriority.HIGH]: 'عالية',
  [ProjectPriority.URGENT]: 'عاجل',
}

// Status and Priority options for forms
export const ProjectStatusOptions = Object.entries(ProjectStatusLabels).map(
  ([value, label]) => ({ value: value as ProjectStatus, label })
)

export const ProjectPriorityOptions = Object.entries(ProjectPriorityLabels).map(
  ([value, label]) => ({ value: value as ProjectPriority, label })
)
