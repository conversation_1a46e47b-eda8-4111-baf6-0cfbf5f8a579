Build a comprehensive Arabic ERP dashboard system called "متبرمج" using Next.js 14 and Django 5.0. This is a multi-phase project that must be implemented incrementally with thorough testing at each stage.

**IMPORTANT: You are encouraged to suggest additional modules, components, and features that would make this a robust, fully integrated, first-class ERP system suitable for large software agencies. The provided specification is a foundation - please think deeply and propose enhancements that would elevate this to enterprise-grade quality.**

## Phase 1: Project Setup & Foundation

### 1.1 Initialize Next.js 14 Project Structure
- Execute: `npx create-next-app@latest nextjs_erp --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"`
- **CRITICAL**: Configure project within the existing workspace directory `/Users/<USER>/Sites/nextjs_erp`
- Create standardized folder structure:
  ```
  src/
  ├── app/                 # App Router pages
  ├── components/          # Reusable UI components
  │   ├── ui/             # Base UI components (buttons, inputs, etc.)
  │   ├── forms/          # Form components
  │   └── layout/         # Layout components (header, sidebar, etc.)
  ├── lib/                # Utility functions and configurations
  ├── types/              # TypeScript type definitions
  ├── hooks/              # Custom React hooks
  ├── stores/             # Zustand state management
  └── constants/          # Application constants
  ```
- Configure absolute imports with `@/` prefix in `tsconfig.json`

### 1.2 Set Up Django 5.0 Backend
- Create Django project: `django-admin startproject erp_backend backend`
- **Database Configuration**: Set up PostgreSQL with these specific settings:
  ```python
  DATABASES = {
      'default': {
          'ENGINE': 'django.db.backends.postgresql',
          'NAME': 'nextjs_erp_db',
          'USER': 'erp_user',
          'PASSWORD': 'secure_password_123',
          'HOST': 'localhost',
          'PORT': '5432',
      }
  }
  ```
- Create Python virtual environment: `python -m venv venv`
- Activate virtual environment: `source venv/bin/activate` (macOS/Linux) or `venv\Scripts\activate` (Windows)
- Create comprehensive `requirements.txt` with exact pinned versions (see Phase 2.2)
- Configure CORS for Next.js development server (localhost:3000) and production domains

### 1.3 Configure Comprehensive Arabic RTL Support
- Install Arabic font: `npm install @fontsource/ibm-plex-sans-arabic`
- Install RTL plugin: `npm install tailwindcss-rtl`
- Configure `tailwind.config.js`:
  ```javascript
  module.exports = {
    content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
    theme: {
      extend: {
        fontFamily: {
          arabic: ['IBM Plex Sans Arabic', 'sans-serif'],
        },
      },
    },
    plugins: [require('tailwindcss-rtl')],
  }
  ```
- Configure `src/app/layout.tsx` with `dir="rtl"` and `lang="ar"`
- Test Arabic text rendering including diacritics (تشكيل) and complex ligatures

### 1.4 Implement Authentication System
- Install NextAuth.js v5: `npm install next-auth@beta`
- Configure JWT provider with Django REST Framework backend integration
- Create authentication pages:
  - `/auth/signin` - Arabic login form with email/password
  - `/auth/signout` - Logout confirmation in Arabic
  - `/auth/register` - User registration (admin-only)
- Implement `middleware.ts` for protected routes with role-based access
- Set up session management with proper TypeScript interfaces

### 1.5 Create Responsive Base Layout Components
- **Header Component**: Arabic navigation menu, user dropdown, notifications bell
- **Sidebar Component**: Collapsible with module icons and Arabic labels, role-based visibility
- **Footer Component**: Company information in Arabic, copyright, version info
- **Logo Integration**: Use `./the_logo.png` from workspace root with proper sizing
- **Currency Display**: Egyptian Pound (ج.م) formatting with Arabic-Indic numerals (٠١٢٣٤٥٦٧٨٩)

## Phase 2: Core Infrastructure Setup

### 2.1 Frontend Dependencies Installation
```bash
# UI and Styling
npm install @tailwindcss/typography@^0.5.10 @headlessui/react@^1.7.17 @heroicons/react@^2.0.18

# Data Management
npm install @tanstack/react-query@^5.8.4 @tanstack/react-table@^8.10.7

# Forms and Validation
npm install react-hook-form@^7.47.0 @hookform/resolvers@^3.3.2 zod@^3.22.4

# State Management and Animation
npm install zustand@^4.4.6 framer-motion@^10.16.4

# Charts and Icons
npm install recharts@^2.8.0 lucide-react@^0.294.0

# Authentication
npm install next-auth@beta @auth/prisma-adapter

# Utilities
npm install date-fns@^2.30.0 clsx@^2.0.0 tailwind-merge@^2.0.0

# Export and Print
npm install react-to-print@^2.14.15 xlsx@^0.18.5

# Notifications
npm install react-hot-toast@^2.4.1
```

### 2.2 Backend Dependencies Installation
```bash
# Core Django
pip install Django==5.0.0 djangorestframework==3.14.0 django-cors-headers==4.3.1

# Database and Caching
pip install psycopg2-binary==2.9.9 redis==5.0.1

# Async and Real-time
pip install celery==5.3.4 django-channels==4.0.0 channels-redis==4.1.0

# Authentication and API
pip install djangorestframework-simplejwt==5.3.0 django-filter==23.5

# Configuration and Utilities
pip install python-decouple==3.8 django-extensions==3.2.3

# API Documentation
pip install drf-spectacular==0.26.5

# File Handling
pip install Pillow==10.1.0 django-storages==1.14.2
```

### 2.3 Database Architecture Setup
- Create Django models for all 13+ modules with:
  - Proper Arabic `verbose_name` and `verbose_name_plural` attributes
  - Foreign key relationships with appropriate `on_delete` behavior
  - Database indexes on frequently queried fields (`db_index=True`)
  - JSON fields for complex data structures
  - Proper field validation and constraints
- Run migrations: `python manage.py makemigrations && python manage.py migrate`
- Configure Redis for caching and session storage:
  ```python
  CACHES = {
      'default': {
          'BACKEND': 'django_redis.cache.RedisCache',
          'LOCATION': 'redis://127.0.0.1:6379/1',
          'OPTIONS': {
              'CLIENT_CLASS': 'django_redis.client.DefaultClient',
          }
      }
  }
  ```

### 2.4 API Foundation Setup
- Configure Django REST Framework with:
  - Custom pagination (25 items per page)
  - Arabic error messages and field labels
  - Proper CORS headers for development and production
- Create base serializers with Arabic validation messages
- Implement generic ViewSets with filtering, searching, and ordering capabilities
- Set up API documentation using drf-spectacular with Arabic descriptions
- Configure proper HTTP status codes and error responses

## Phase 3: Module Implementation (Sequential Order)

### Priority 1: Projects Module (Complete Implementation)

**Django Model (Enhanced):**
```python
from django.db import models
from django.core.validators import URLValidator, MinValueValidator
from django.contrib.auth.models import User

class ProjectStatus(models.TextChoices):
    PLANNING = 'planning', 'التخطيط'
    IN_PROGRESS = 'in_progress', 'قيد التنفيذ'
    TESTING = 'testing', 'الاختبار'
    COMPLETED = 'completed', 'مكتمل'
    ON_HOLD = 'on_hold', 'متوقف مؤقتاً'
    CANCELLED = 'cancelled', 'ملغي'

class ProjectPriority(models.TextChoices):
    LOW = 'low', 'منخفضة'
    MEDIUM = 'medium', 'متوسطة'
    HIGH = 'high', 'عالية'
    URGENT = 'urgent', 'عاجل'

class Project(models.Model):
    # Basic Information
    name = models.CharField(max_length=200, verbose_name="اسم المشروع", db_index=True)
    description = models.TextField(blank=True, verbose_name="وصف المشروع")
    client = models.ForeignKey('clients.Client', on_delete=models.CASCADE, verbose_name="العميل")
    
    # Dates and Timeline
    start_date = models.DateField(verbose_name="تاريخ البداية", db_index=True)
    expected_end_date = models.DateField(verbose_name="تاريخ الانتهاء المتوقع")
    actual_end_date = models.DateField(null=True, blank=True, verbose_name="تاريخ الانتهاء الفعلي")
    
    # Project Management
    status = models.CharField(max_length=20, choices=ProjectStatus.choices, default=ProjectStatus.PLANNING, verbose_name="حالة المشروع")
    priority = models.CharField(max_length=10, choices=ProjectPriority.choices, default=ProjectPriority.MEDIUM, verbose_name="الأولوية")
    progress_percentage = models.PositiveIntegerField(default=0, validators=[MinValueValidator(0)], verbose_name="نسبة الإنجاز")
    
    # Team and Resources
    project_manager = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='managed_projects', verbose_name="مدير المشروع")
    project_team = models.ManyToManyField('developers.Developer', blank=True, verbose_name="فريق المشروع")
    estimated_hours = models.PositiveIntegerField(default=0, verbose_name="الساعات المقدرة")
    actual_hours = models.PositiveIntegerField(default=0, verbose_name="الساعات الفعلية")
    
    # Technical Details
    implementation_phases = models.JSONField(default=list, verbose_name="مراحل التنفيذ")
    technologies_used = models.JSONField(default=list, verbose_name="التقنيات المستخدمة")
    
    # Domain and Server Information
    domain_link = models.URLField(blank=True, null=True, verbose_name="رابط الدومين")
    domain_email = models.EmailField(blank=True, verbose_name="إيميل الدومين")
    domain_password = models.CharField(max_length=100, blank=True, verbose_name="كلمة مرور الدومين")
    server_link = models.URLField(blank=True, null=True, verbose_name="رابط السيرفر")
    server_email = models.EmailField(blank=True, verbose_name="إيميل السيرفر")
    server_password = models.CharField(max_length=100, blank=True, verbose_name="كلمة مرور السيرفر")
    
    # Communication
    whatsapp_group_link = models.URLField(blank=True, null=True, verbose_name="رابط مجموعة الواتساب")
    slack_channel = models.CharField(max_length=100, blank=True, verbose_name="قناة سلاك")
    
    # Financial
    budget = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="الميزانية")
    actual_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name="التكلفة الفعلية")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_projects', verbose_name="أنشئ بواسطة")

    class Meta:
        verbose_name = "مشروع"
        verbose_name_plural = "المشاريع"
        ordering = ['-start_date', '-priority']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['start_date', 'expected_end_date']),
            models.Index(fields=['client', 'status']),
        ]

    def __str__(self):
        return f"{self.name} - {self.client.name}"

    @property
    def is_overdue(self):
        from django.utils import timezone
        return self.expected_end_date < timezone.now().date() and self.status != ProjectStatus.COMPLETED

    @property
    def days_remaining(self):
        from django.utils import timezone
        if self.status == ProjectStatus.COMPLETED:
            return 0
        return (self.expected_end_date - timezone.now().date()).days
```

**Frontend Implementation Requirements:**
- Create `/app/projects/` route with complete CRUD interface
- Implement TanStack Table with:
  - Arabic column headers with proper RTL alignment
  - Sortable columns with Arabic sort indicators
  - Row selection with bulk actions
  - Responsive design that collapses to cards on mobile
- Advanced filtering system:
  - Client dropdown with search
  - Date range picker (start date, end date)
  - Status multi-select with Arabic labels
  - Priority filter
  - Team member multi-select
  - Progress range slider
- Debounced search across: name, description, client name, domain
- Export functionality:
  - PDF export with Arabic text support
  - Excel export using xlsx library
  - Print-friendly view using react-to-print
- Form validation using Zod with Arabic error messages
- Loading states, error boundaries, and toast notifications in Arabic

### Sequential Module Implementation Order:

1. **Clients Module** - Enhanced with relationship management
   - Include `mood` field: `[("رخم", "رخم"), ("طيب", "طيب"), ("مجنون", "مجنون")]`
   - Add contact history tracking
   - Client satisfaction ratings
   - Communication preferences

2. **Tasks Module** - Advanced task management
   - Include `type` field: `[("خفيفة", "خفيفة"), ("متوسطة", "متوسطة"), ("تقيلة فشخ", "تقيلة فشخ")]`
   - Task dependencies and subtasks
   - Time tracking with start/stop functionality
   - Task templates for common workflows

3. **Sales Team Module** - Enhanced sales tracking
   - Predefined members: `["الدبور", "عبدو نصر", "الدكتور ميكي", "مستر بيبو", "مستر اكس", "جوني جونيور"]`
   - Sales pipeline management
   - Commission tracking
   - Performance metrics and KPIs

4. **Media Buyers Module** - Comprehensive campaign management
   - Campaign ROI tracking
   - Budget allocation and spending
   - Platform-specific metrics (Facebook, Google, etc.)
   - A/B testing results

5. **Developers Module** - Advanced developer management
   - Skills matrix with proficiency levels
   - Availability calendar
   - Code review assignments
   - Performance evaluations

6. **Designers Module** - Creative workflow management
   - Portfolio management with file uploads
   - Design approval workflows
   - Brand guidelines compliance
   - Asset library management

7. **WordPress Developers Module** - Specialized WordPress features
   - Plugin and theme management
   - Site maintenance schedules
   - Security monitoring
   - Performance optimization tracking

8. **Annual Renewals Module** - Automated renewal management
   - Automatic notification system
   - Renewal probability scoring
   - Contract value tracking
   - Client retention analytics

9. **Packages Module** - Enhanced package management
   - Types: `[("باقة المجد", "باقة المجد"), ("باقة القمة", "باقة القمة")]`
   - Package customization options
   - Pricing tiers and discounts
   - Feature comparison matrix

10. **Expenses Module** - Comprehensive financial tracking
    - Multi-currency support (EGP/USD) with real-time conversion
    - Expense categories and subcategories
    - Receipt management with file uploads
    - Budget vs. actual reporting

11. **Reports Module** - Advanced analytics dashboard
    - Interactive charts using Recharts
    - Custom report builder
    - Scheduled report generation
    - Data export in multiple formats

12. **System Settings Module** - Complete system administration
    - User role management
    - System configuration
    - Backup and restore functionality
    - Audit logs and activity tracking

## Phase 4: Advanced Features Integration

### 4.1 Real-time Notifications (Django Channels)
- Configure WebSocket consumers for:
  - Project status updates
  - Task assignments and completions
  - Client communications
  - System alerts
- Implement notification models with read/unread status
- Create real-time UI updates using WebSocket connections
- Add notification bell with unread count and dropdown

### 4.2 Role-based Access Control (RBAC)
- Define Django groups: Admin, Manager, Developer, Designer, Sales, Client
- Implement permission decorators for API endpoints
- Create role-specific dashboard layouts and navigation
- Add middleware for route-level permission checking
- Implement field-level permissions for sensitive data

### 4.3 Advanced Search and Analytics
- Implement full-text search using PostgreSQL's built-in capabilities
- Add global search component with autocomplete
- Create advanced filter modals with multiple criteria
- Implement saved search functionality
- Add search analytics and popular searches

### 4.4 Performance Optimization
- Configure Redis caching with 5-minute TTL for API responses
- Implement React Query with proper cache invalidation strategies
- Add database query optimization using select_related and prefetch_related
- Create proper database indexes for all foreign keys and search fields
- Implement lazy loading for large datasets

## Technical Requirements & Standards

### Language and Localization
- **Primary Language**: Arabic with complete RTL support
- **HTML Direction**: `dir="rtl"` on html element
- **Font**: IBM Plex Sans Arabic with fallback to system Arabic fonts
- **Number Format**: Arabic-Indic numerals (٠١٢٣٤٥٦٧٨٩) for display, Western numerals for input

### Currency and Formatting
- **Primary Currency**: Egyptian Pound (ج.م) using Intl.NumberFormat
- **Secondary Currency**: USD ($) with real-time conversion rates
- **Date Format**: Arabic calendar with Hijri date support
- **Time Format**: 24-hour format with Arabic AM/PM indicators

### Responsive Design Standards
- **Mobile-first approach** with Tailwind breakpoints:
  - sm: 640px (small tablets)
  - md: 768px (tablets)
  - lg: 1024px (small desktops)
  - xl: 1280px (desktops)
  - 2xl: 1536px (large desktops)
- **Touch-friendly interfaces** with minimum 44px touch targets
- **Accessible navigation** with keyboard support and screen reader compatibility

### Database and API Standards
- **Database**: PostgreSQL 15+ with proper constraints and indexes
- **API Design**: RESTful with kebab-case URLs and consistent naming
- **HTTP Status Codes**: Proper usage (200, 201, 400, 401, 403, 404, 500)
- **Response Format**: Consistent JSON structure with Arabic error messages

### Authentication and Security
- **JWT Tokens**: 15-minute access tokens, 7-day refresh tokens
- **Cookie Security**: httpOnly, secure, sameSite attributes
- **Password Policy**: Minimum 8 characters with complexity requirements
- **Rate Limiting**: API endpoint protection against abuse

### State Management and Forms
- **State Management**: Separate Zustand stores per module with TypeScript interfaces
- **Form Validation**: React Hook Form with Zod schemas and Arabic error messages
- **Data Persistence**: Local storage for user preferences, session storage for temporary data

### Error Handling and Logging
- **Frontend**: React Error Boundaries with user-friendly Arabic messages
- **Backend**: Comprehensive logging with structured format
- **Notifications**: Toast notifications using react-hot-toast
- **Monitoring**: Error tracking and performance monitoring

## Implementation Guidelines

### Development Workflow
1. **Complete Phase 1 entirely** before proceeding - verify each component works independently
2. **Test each module thoroughly** with unit tests (Jest) and integration tests (Django TestCase)
3. **Verify Arabic text rendering** across browsers (Chrome, Firefox, Safari) and zoom levels
4. **Test RTL layout** on all screen sizes using browser developer tools
5. **Validate API endpoints** using Postman/Thunder Client with comprehensive test cases

### Code Quality Standards
- **TypeScript**: Strict mode enabled, no `any` types allowed
- **ESLint**: Configured with Arabic-specific rules and accessibility checks
- **Prettier**: Configured for consistent code formatting
- **Naming Conventions**: Arabic for UI text, English for code variables/functions
- **Documentation**: Comprehensive JSDoc comments and README files

### Testing Requirements
- **Unit Tests**: All utility functions, hooks, and components
- **Integration Tests**: API endpoints with proper fixtures
- **E2E Tests**: Critical user workflows using Playwright
- **Accessibility Tests**: WCAG 2.1 AA compliance
- **Performance Tests**: Core Web Vitals and load testing

## Success Criteria for Each Phase

### Phase 1 Success Criteria
- ✅ Authentication system fully functional with JWT tokens
- ✅ Arabic interface renders correctly with proper RTL layout
- ✅ Responsive design works on all device sizes
- ✅ Logo integration and branding elements in place
- ✅ Basic navigation and routing functional

### Phase 2 Success Criteria
- ✅ All dependencies installed and configured correctly
- ✅ Database connected with proper migrations
- ✅ API endpoints responding with correct CORS headers
- ✅ Redis caching operational
- ✅ Development environment fully set up

### Phase 3 Success Criteria
- ✅ Each module has complete CRUD operations
- ✅ Responsive design tested on multiple devices
- ✅ Form validation working with Arabic error messages
- ✅ Export functionality operational with Arabic content
- ✅ Search and filtering features functional

### Phase 4 Success Criteria
- ✅ Real-time notifications working across all modules
- ✅ Role-based access control properly implemented
- ✅ Advanced search functionality operational
- ✅ Performance optimization targets met
- ✅ System ready for production deployment

## Additional Enterprise Features (Your Suggestions Welcome)

**Please suggest enhancements in these areas:**
1. **Advanced Analytics**: Business intelligence, predictive analytics, custom dashboards
2. **Integration Capabilities**: Third-party API integrations, webhook support, data synchronization
3. **Workflow Automation**: Business process automation, approval workflows, automated notifications
4. **Document Management**: File versioning, document templates, digital signatures
5. **Communication Tools**: Internal messaging, video conferencing integration, announcement system
6. **Mobile Application**: React Native companion app for mobile access
7. **API Gateway**: Rate limiting, API versioning, developer portal
8. **Backup and Recovery**: Automated backups, disaster recovery, data archiving
9. **Compliance and Security**: GDPR compliance, security auditing, penetration testing
10. **Scalability Features**: Microservices architecture, load balancing, auto-scaling

**Begin implementation immediately with Phase 1, ensuring each step is completed and thoroughly tested before proceeding. After completing each phase, run comprehensive tests and verify all functionality works as expected.**